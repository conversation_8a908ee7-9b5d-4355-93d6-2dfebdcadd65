﻿#region 程序集 SQlFun, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// G:\project\1.立中百检_内存泄漏\DLL\SQlFun.dll
// Decompiled with ICSharpCode.Decompiler 8.2.0.7535
#endregion

using System;
using System.Data;
using System.Data.SqlClient;
using System.Windows.Forms;
using GemBox.ExcelLite;


public class SQlFun
{
    private SqlCommand cmd;

    private SqlDataAdapter sda;

    private DataSet ds;

    public DataTable dTable;

    private static string constr = "Server=DESKTOP-F804JHV;Database=MyDB;Trusted_Connection=SSPI";

    public SqlConnection conn;

    public void connection(string connectionstr1)
    {
        conn = new SqlConnection(connectionstr1);
    }

    public bool Sql_open()
    {
        try
        {
            conn.Open();
            return true;
        }
        catch
        {
            return false;
        }
    }

    public int Sql_write_hatdeepprepare(int index, string wheeltype, double rec1row1, double rec1column1, double rec1row2, double rec1column2, double rec2row1, double rec2column1, double rec2row2, double rec2column2, double offsetval)
    {
        if (conn.State == ConnectionState.Open)
        {
            string cmdText = "USE MyDB insert into 三维检测配方(序号,轮毂型号,矩形1Row1,矩形1Column1,矩形1Row2,矩形1Column2,矩形2Row1,矩形2Column1,矩形2Row2,矩形2Column2,测量点偏移) values\r\n(" + index + ",'" + wheeltype + "'," + rec1row1 + "," + rec1column1 + "," + rec1row2 + "," + rec1column2 + "," + rec2row1 + "," + rec2column1 + "," + rec2row2 + "," + rec2column2 + "," + offsetval + ")";
            cmd = new SqlCommand(cmdText, conn);
            return cmd.ExecuteNonQuery();
        }

        MessageBox.Show("数据库未连接！");
        return 0;
    }

    public int Sql_indexmax(string tablename)
    {
        int result = 0;
        string cmdText = "USE MyDB select max(序号) from " + tablename;
        cmd = new SqlCommand(cmdText, conn);
        SqlDataReader sqlDataReader = cmd.ExecuteReader();
        while (sqlDataReader.Read())
        {
            string text = Convert.ToString(sqlDataReader.GetValue(0));
            if (text != "")
            {
                result = int.Parse(sqlDataReader[0].ToString());
            }
        }

        sqlDataReader.Close();
        return result;
    }

    public void Sql_checkbytime(string start, string end, string wheeltype, DataGridView dataGridView)
    {
        string text = "";
        text = ((!(wheeltype != "")) ? ("USE MyDB select * from 检测结果 where 时间 between '" + start + "' and '" + end + "' order by 序号") : ("USE MyDB select * from 检测结果 where 时间 between '" + start + "' and '" + end + "' and 轮毂型号='" + wheeltype + "' order by 序号"));
        if (conn.State == ConnectionState.Open)
        {
            sda = new SqlDataAdapter();
            cmd = new SqlCommand(text, conn);
            sda.SelectCommand = cmd;
            dTable = new DataTable();
            sda.Fill(dTable);
            dataGridView.DataSource = dTable;
        }
        else
        {
            MessageBox.Show("数据库未连接！");
        }
    }

    public int Sql_write_wheeltype(int index, string wheeltype, string color, string message)
    {
        if (conn.State == ConnectionState.Open)
        {
            string cmdText = "USE MyDB insert into 轮型识别1(序号, 轮毂型号,颜色,信息) values(" + index + ",'" + wheeltype + "','" + color + "','" + message + "')";
            cmd = new SqlCommand(cmdText, conn);
            return cmd.ExecuteNonQuery();
        }

        MessageBox.Show("数据库未连接！");
        return 0;
    }

    public int Sql_write_wheeltypetotemporaryarea(string tablename, string wheeltype)
    {
        if (conn.State == ConnectionState.Open)
        {
            string cmdText = "USE MyDB insert into " + tablename + "(轮毂型号) values( '" + wheeltype + "')";
            cmd = new SqlCommand(cmdText, conn);
            return cmd.ExecuteNonQuery();
        }

        MessageBox.Show("数据库未连接！");
        return 0;
    }

    public int Sql_write_resulttotemporaryarea(string tablename, PassData passdata)
    {
        if (conn.State == ConnectionState.Open)
        {
            string cmdText = "USE MyDB insert into 软件数据交互(螺栓孔1厚度,螺栓孔1厚度检测偏差,螺栓孔1厚度检测结果,螺栓孔2厚度,螺栓孔2厚度检测偏差,螺栓孔2厚度检测结果,\r\n螺栓孔3厚度,螺栓孔3厚度检测偏差,螺栓孔3厚度检测结果,螺栓孔4厚度,螺栓孔4厚度检测偏差,螺栓孔4厚度检测结果,螺栓孔5厚度,螺栓孔5厚度检测偏差,螺栓孔5厚度检测结果,\r\n螺栓孔6厚度,螺栓孔6厚度检测偏差,螺栓孔6厚度检测结果,螺栓孔7厚度,螺栓孔7厚度检测偏差,螺栓孔7厚度检测结果,螺栓孔8厚度,螺栓孔8厚度检测偏差,螺栓孔8厚度检测结果) values(" + passdata.boltthinkness_val[0] + "," + passdata.boltthinknesscompareval[0] + ",'" + passdata.boltthinkness_result[0] + "'," + passdata.boltthinkness_val[1] + "," + passdata.boltthinknesscompareval[1] + ",'" + passdata.boltthinkness_result[1] + "'," + passdata.boltthinkness_val[2] + "," + passdata.boltthinknesscompareval[2] + ",'" + passdata.boltthinkness_result[2] + "'," + passdata.boltthinkness_val[3] + "," + passdata.boltthinknesscompareval[3] + ",'" + passdata.boltthinkness_result[3] + "'," + passdata.boltthinkness_val[4] + "," + passdata.boltthinknesscompareval[4] + ",'" + passdata.boltthinkness_result[4] + "'," + passdata.boltthinkness_val[5] + "," + passdata.boltthinknesscompareval[5] + ",'" + passdata.boltthinkness_result[5] + "'," + passdata.boltthinkness_val[6] + "," + passdata.boltthinknesscompareval[6] + ",'" + passdata.boltthinkness_result[6] + "'," + passdata.boltthinkness_val[7] + "," + passdata.boltthinknesscompareval[7] + ",'" + passdata.boltthinkness_result[7] + "'," + passdata.boltthinkness_val[8] + "," + passdata.boltthinknesscompareval[8] + ",'" + passdata.boltthinkness_result[8] + "')";
            cmd = new SqlCommand(cmdText, conn);
            return cmd.ExecuteNonQuery();
        }

        MessageBox.Show("数据库未连接！");
        return 0;
    }

    public int Sql_write_checkresult(int index, PassData passdata)
    {
        if (conn.State == ConnectionState.Open)
        {
            string cmdText = "USE MyDB insert into 检测结果(序号, 轮毂型号,帽槽深度检测值1,帽槽深度检测值2,帽槽深度偏差,帽槽深度检测结果,\r\n中心孔检测值,中心孔偏差,中心孔检测结果,螺栓孔1检测值,螺栓孔1偏差,螺栓孔1检测结果,螺栓孔2检测值,螺栓孔2偏差,螺栓孔2检测结果,\r\n螺栓孔3检测值,螺栓孔3偏差,螺栓孔3检测结果,螺栓孔4检测值,螺栓孔4偏差,螺栓孔4检测结果,螺栓孔5检测值,螺栓孔5偏差,螺栓孔5检测结果,\r\n螺栓孔6检测值,螺栓孔6偏差,螺栓孔6检测结果,螺栓孔7检测值,螺栓孔7偏差,螺栓孔7检测结果,螺栓孔8检测值,螺栓孔8偏差,螺栓孔8检测结果,螺栓孔1直径,螺栓孔1直径偏差,螺栓孔1直径检测结果,螺栓孔2直径,螺栓孔2直径偏差,螺栓孔2直径检测结果,\r\n螺栓孔3直径,螺栓孔3直径偏差,螺栓孔3直径检测结果,螺栓孔4直径,螺栓孔4直径偏差,螺栓孔4直径检测结果,螺栓孔5直径,螺栓孔5直径偏差,螺栓孔5直径检测结果,\r\n螺栓孔6直径,螺栓孔6直径偏差,螺栓孔6直径检测结果,螺栓孔7直径,螺栓孔7直径偏差,螺栓孔7直径检测结果,螺栓孔8直径,螺栓孔8直径偏差,螺栓孔8直径检测结果,\r\n螺栓孔1厚度,螺栓孔1厚度偏差,螺栓孔1厚度检测结果,螺栓孔2厚度,螺栓孔2厚度偏差,螺栓孔2厚度检测结果,螺栓孔3厚度,螺栓孔3厚度偏差,螺栓孔3厚度检测结果,螺栓孔4厚度,螺栓孔4厚度偏差,螺栓孔4厚度检测结果,\r\n螺栓孔5厚度,螺栓孔5厚度偏差,螺栓孔5厚度检测结果,螺栓孔6厚度,螺栓孔6厚度偏差,螺栓孔6厚度检测结果,螺栓孔7厚度,螺栓孔7厚度偏差,螺栓孔7厚度检测结果,螺栓孔8厚度,螺栓孔8厚度偏差,螺栓孔8厚度检测结果,\r\n帽止口检测值,帽止口偏差,帽止口检测结果,最后结果) values(" + index.ToString() + ",'" + passdata.wheeltype + "'," + passdata.laserdeep_val[0] + "," + passdata.laserdeep_val[1] + "," + passdata.lasercompareval + ",'" + passdata.deep_result + "'," + passdata.cen_val + "," + passdata.cencompareval + ",'" + passdata.cenhol_result + "'," + passdata.position_val[0] + "," + passdata.positioncompareval[0] + ",'" + passdata.pos_result[0] + "'," + passdata.position_val[1] + "," + passdata.positioncompareval[1] + ",'" + passdata.pos_result[1] + "'," + passdata.position_val[2] + "," + passdata.positioncompareval[2] + ",'" + passdata.pos_result[2] + "'," + passdata.position_val[3] + "," + passdata.positioncompareval[3] + ",'" + passdata.pos_result[3] + "'," + passdata.position_val[4] + "," + passdata.positioncompareval[4] + ",'" + passdata.pos_result[4] + "'," + passdata.position_val[5] + "," + passdata.positioncompareval[5] + ",'" + passdata.pos_result[5] + "'," + passdata.position_val[6] + "," + passdata.positioncompareval[6] + ",'" + passdata.pos_result[6] + "'," + passdata.position_val[7] + "," + passdata.positioncompareval[7] + ",'" + passdata.pos_result[7] + "'," + passdata.boltdiameter_val[0] + "," + passdata.boltdiametercompareval[0] + ",'" + passdata.boltdiameter_result[0] + "'," + passdata.boltdiameter_val[1] + "," + passdata.boltdiametercompareval[1] + ",'" + passdata.boltdiameter_result[1] + "'," + passdata.boltdiameter_val[2] + "," + passdata.boltdiametercompareval[2] + ",'" + passdata.boltdiameter_result[2] + "'," + passdata.boltdiameter_val[3] + "," + passdata.boltdiametercompareval[3] + ",'" + passdata.boltdiameter_result[3] + "'," + passdata.boltdiameter_val[4] + "," + passdata.boltdiametercompareval[4] + ",'" + passdata.boltdiameter_result[4] + "'," + passdata.boltdiameter_val[5] + "," + passdata.boltdiametercompareval[5] + ",'" + passdata.boltdiameter_result[5] + "'," + passdata.boltdiameter_val[6] + "," + passdata.boltdiametercompareval[6] + ",'" + passdata.boltdiameter_result[6] + "'," + passdata.boltdiameter_val[7] + "," + passdata.boltdiametercompareval[7] + ",'" + passdata.boltdiameter_result[7] + "'," + passdata.boltthinkness_val[0] + "," + passdata.boltthinknesscompareval[0] + ",'" + passdata.boltthinkness_result[0] + "'," + passdata.boltthinkness_val[1] + "," + passdata.boltthinknesscompareval[1] + ",'" + passdata.boltthinkness_result[1] + "'," + passdata.boltthinkness_val[2] + "," + passdata.boltthinknesscompareval[2] + ",'" + passdata.boltthinkness_result[2] + "'," + passdata.boltthinkness_val[3] + "," + passdata.boltthinknesscompareval[3] + ",'" + passdata.boltthinkness_result[3] + "'," + passdata.boltthinkness_val[4] + "," + passdata.boltthinknesscompareval[4] + ",'" + passdata.boltthinkness_result[4] + "'," + passdata.boltthinkness_val[5] + "," + passdata.boltthinknesscompareval[5] + ",'" + passdata.boltthinkness_result[5] + "'," + passdata.boltthinkness_val[6] + "," + passdata.boltthinknesscompareval[6] + ",'" + passdata.boltthinkness_result[6] + "'," + passdata.boltthinkness_val[7] + "," + passdata.boltthinknesscompareval[7] + ",'" + passdata.boltthinkness_result[7] + "'," + passdata.hat_val + "," + passdata.hatcompareval + ",'" + passdata.hathol_result + "','" + passdata.last_result + "')";
            cmd = new SqlCommand(cmdText, conn);
            return cmd.ExecuteNonQuery();
        }

        MessageBox.Show("数据库未连接！");
        return 0;
    }

    public int Sql_write_searchregion(int index, string wheeltype, double radius)
    {
        if (conn.State == ConnectionState.Open)
        {
            string cmdText = "USE MyDB insert into 气门孔范围直径(序号,轮毂型号,半径) values(" + index.ToString() + ",'" + wheeltype + "'," + radius + ")";
            cmd = new SqlCommand(cmdText, conn);
            return cmd.ExecuteNonQuery();
        }

        MessageBox.Show("数据库未连接！");
        return 0;
    }

    public int Sql_write_cenholeprepare(int index, string wheeltype, double centrerow, double centrecolumn, double stdpixdiameter, double stddiameter, double stdratio)
    {
        if (conn.State == ConnectionState.Open)
        {
            string cmdText = "USE MyDB insert into 中心孔位置度帽止口参数(序号,轮毂型号,中心孔Row,中心孔Column,中心孔像素直径,中心孔几何直径,中心孔比例) values(" + index + ",'" + wheeltype + "'," + centrerow + "," + centrecolumn + "," + stdpixdiameter + "," + stddiameter + "," + stdratio + ")";
            cmd = new SqlCommand(cmdText, conn);
            return cmd.ExecuteNonQuery();
        }

        MessageBox.Show("数据库未连接！");
        return 0;
    }

    public int Sql_write_boltholeprepare(string wheeltype, int holenum, double[] stdpixdiameter, double[] stddiameter, double[] stdratio, double stdboltrow, double stdboltcolumn, double stdboltradius)
    {
        if (conn.State == ConnectionState.Open)
        {
            int num = 0;
            string cmdText;
            for (int i = 1; i <= holenum; i++)
            {
                cmdText = "USE MyDB UPDATE  中心孔位置度帽止口参数 SET 螺栓孔" + i.ToString() + "像素直径 =" + stdpixdiameter[i - 1] + ",螺栓孔" + i.ToString() + "几何直径 = " + stddiameter[i - 1] + ",螺栓孔" + i.ToString() + "比例 = " + stdratio[i - 1] + " WHERE 轮毂型号 ='" + wheeltype + "'";
                cmd = new SqlCommand(cmdText, conn);
                num = cmd.ExecuteNonQuery();
            }

            cmdText = "USE MyDB UPDATE  中心孔位置度帽止口参数 SET 螺栓孔1Row = " + stdboltrow + ",螺栓孔1Column = " + stdboltcolumn + ",螺栓孔1Radius = " + stdboltradius + " WHERE 轮毂型号 = '" + wheeltype + "'";
            cmd = new SqlCommand(cmdText, conn);
            return cmd.ExecuteNonQuery();
        }

        MessageBox.Show("数据库未连接！");
        return 0;
    }

    public int Sql_write_hatholeprepare(int index, string wheeltype, double centrerow, double centrecolumn, double stdpixdiameter, double stddiameter, double stdratio)
    {
        if (conn.State == ConnectionState.Open)
        {
            string cmdText = "USE MyDB insert into 中心孔位置度帽止口参数(序号,轮毂型号,帽止口Row,帽止口Column,帽止口像素直径,帽止口几何直径,帽止口比例) values(" + index + ",'" + wheeltype + "'," + centrerow + "," + centrecolumn + "," + stdpixdiameter + "," + stddiameter + "," + stdratio + ")";
            cmd = new SqlCommand(cmdText, conn);
            return cmd.ExecuteNonQuery();
        }

        MessageBox.Show("数据库未连接！");
        return 0;
    }

    public bool Sql_ExistColumn(string tablename, string columnname, string ColumnItem)
    {
        if (conn.State == ConnectionState.Open)
        {
            string cmdText = "USE MyDB SELECT " + columnname + " from " + tablename;
            sda = new SqlDataAdapter();
            cmd = new SqlCommand(cmdText, conn);
            sda.SelectCommand = cmd;
            dTable = new DataTable();
            sda.Fill(dTable);
        }

        for (int i = 0; i < dTable.Rows.Count; i++)
        {
            DataRow dataRow = dTable.Rows[i];
            if (ColumnItem == dataRow[0].ToString().Trim())
            {
                return true;
            }
        }

        return false;
    }

    public void Sql_read(string tablename)
    {
        if (conn.State == ConnectionState.Open)
        {
            string cmdText = "USE MyDB SELECT * FROM " + tablename;
            cmd = new SqlCommand(cmdText, conn);
            SqlDataReader sqlDataReader = cmd.ExecuteReader();
            while (sqlDataReader.Read())
            {
                MessageBox.Show(sqlDataReader[0].ToString() + sqlDataReader[1].ToString());
            }
        }
        else
        {
            MessageBox.Show("数据库未连接！");
        }
    }

    public void Sql_Find(string columnname, string tablename, string wheeltype, out double regionsize)
    {
        if (conn.State == ConnectionState.Open)
        {
            string cmdText = "USE MyDB SELECT " + columnname + " FROM " + tablename + " where 轮毂型号=  '" + wheeltype + "'";
            sda = new SqlDataAdapter();
            cmd = new SqlCommand(cmdText, conn);
            sda.SelectCommand = cmd;
            dTable = new DataTable();
            sda.Fill(dTable);
            DataRow dataRow = dTable.Rows[0];
            string text = dataRow[0].ToString().Trim();
            if (text != "")
            {
                regionsize = double.Parse(text);
            }
            else
            {
                regionsize = 0.0;
            }
        }
        else
        {
            MessageBox.Show("数据库未连接！");
            regionsize = 0.0;
        }
    }

    public void Sql_Getwheeltypeorresult(string columnname, string tablename, out string wheeltype)
    {
        if (conn.State == ConnectionState.Open)
        {
            string cmdText = "USE MyDB SELECT " + columnname + " FROM " + tablename;
            sda = new SqlDataAdapter();
            cmd = new SqlCommand(cmdText, conn);
            sda.SelectCommand = cmd;
            dTable = new DataTable();
            sda.Fill(dTable);
            DataRow dataRow = dTable.Rows[0];
            string text = dataRow[0].ToString().Trim();
            if (text != "")
            {
                wheeltype = text;
            }
            else
            {
                wheeltype = "";
            }
        }
        else
        {
            MessageBox.Show("数据库未连接！");
            wheeltype = "";
        }
    }

    public int Sql_modify(string tablename, string columnname, string typename, double values)
    {
        if (conn.State == ConnectionState.Open)
        {
            string cmdText = "USE MyDB update " + tablename + " Set " + columnname + " = " + values + " where 轮毂型号 = '" + typename + "'";
            cmd = new SqlCommand(cmdText, conn);
            return cmd.ExecuteNonQuery();
        }

        MessageBox.Show("数据库未连接！");
        return 0;
    }

    public void Sql_deleterow(int index)
    {
        string cmdText = "USE MyDB DELETE FROM 轮型识别1 WHERE 序号 = " + index;
        cmd = new SqlCommand(cmdText, conn);
        int num = cmd.ExecuteNonQuery();
    }

    public void Sql_cleartable(string tablename)
    {
        string cmdText = "TRUNCATE TABLE " + tablename;
        cmd = new SqlCommand(cmdText, conn);
        int num = cmd.ExecuteNonQuery();
    }

    public void disp_datagridview(string tablename, DataGridView dataGridView, double width)
    {
        string cmdText = "USE MyDB SELECT * FROM " + tablename + " ORDER BY 序号";
        if (conn.State == ConnectionState.Open)
        {
            sda = new SqlDataAdapter();
            cmd = new SqlCommand(cmdText, conn);
            sda.SelectCommand = cmd;
            dTable = new DataTable();
            sda.Fill(dTable);
            dataGridView.DataSource = dTable;
            dataGridView.Columns[0].Width = (int)Math.Round(width * 0.05);
            dataGridView.Columns[1].Width = (int)Math.Round(width * 0.2);
            dataGridView.Columns[2].Width = (int)Math.Round(width * 0.55);
            dataGridView.Columns[3].Width = (int)Math.Round(width * 0.2);
        }
        else
        {
            MessageBox.Show("数据库未连接！");
        }
    }

    public void fill_datatable(string tablename)
    {
        string cmdText = "USE MyDB SELECT * FROM " + tablename + " ORDER BY 序号";
        if (conn.State == ConnectionState.Open)
        {
            sda = new SqlDataAdapter();
            cmd = new SqlCommand(cmdText, conn);
            sda.SelectCommand = cmd;
            dTable = new DataTable();
            sda.Fill(dTable);
        }
        else
        {
            MessageBox.Show("数据库未连接！");
        }
    }

    public void disp3days_datagridview(string tablename, DataGridView dataGridView, double width)
    {
        string cmdText = "USE MyDB select * from " + tablename + "  where datediff(day,时间,getdate())<= 2 and datediff(day,时间,getdate())>= 0  ORDER BY 序号";
        if (conn.State == ConnectionState.Open)
        {
            sda = new SqlDataAdapter();
            cmd = new SqlCommand(cmdText, conn);
            sda.SelectCommand = cmd;
            dTable = new DataTable();
            sda.Fill(dTable);
            dataGridView.DataSource = dTable;
            dataGridView.Columns[0].Width = (int)Math.Round(width * 0.05);
            dataGridView.Columns[1].Width = (int)Math.Round(width * 0.2);
            dataGridView.Columns[2].Width = (int)Math.Round(width * 0.55);
            dataGridView.Columns[3].Width = (int)Math.Round(width * 0.2);
        }
        else
        {
            MessageBox.Show("数据库未连接！");
        }
    }

    public void Export_DataGridViewtoExcel(DataGridView dataGridView)
    {
        if (dataGridView.Rows.Count <= 0)
        {
            MessageBox.Show("没有数据可供导出！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
            return;
        }

        if (dataGridView.Columns.Count <= 0)
        {
            MessageBox.Show("没有数据可供导出！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
            return;
        }

        if (dataGridView.Rows.Count > 65536)
        {
            MessageBox.Show("数据记录数太多（最多不能超过65536条），不能保存！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
            return;
        }

        if (dataGridView.Columns.Count > 256)
        {
            MessageBox.Show("数据记录数太多（最多不能超过256列），不能保存！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
            return;
        }

        SaveFileDialog saveFileDialog = new SaveFileDialog();
        saveFileDialog.Filter = "Excel files (*.xls)|*.xls";
        saveFileDialog.FilterIndex = 0;
        saveFileDialog.RestoreDirectory = true;
        saveFileDialog.CreatePrompt = false;
        saveFileDialog.Title = "导出为Excel文件";
        if (saveFileDialog.ShowDialog() != DialogResult.OK)
        {
            return;
        }

        ExcelFile excelFile = new ExcelFile();
        ExcelWorksheet excelWorksheet = excelFile.Worksheets.Add("Account");
        try
        {
            for (int i = 0; i < dataGridView.Columns.Count; i++)
            {
                excelWorksheet.Cells[0, i].Value = dataGridView.Columns[i].HeaderText;
            }

            for (int j = 0; j < dataGridView.Rows.Count - 1; j++)
            {
                DataGridViewRow dataGridViewRow = dataGridView.Rows[j];
                for (int k = 0; k < dataGridViewRow.Cells.Count; k++)
                {
                    if (k < dataGridView.Columns.Count - 2)
                    {
                        excelWorksheet.Cells[j + 1, k].Value = dataGridViewRow.Cells[k].Value.ToString();
                    }
                    else
                    {
                        excelWorksheet.Cells[j + 1, k].Value = dataGridViewRow.Cells[k].Value.ToString();
                    }
                }
            }

            excelFile.SaveXls(saveFileDialog.FileName);
        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
            return;
        }

        MessageBox.Show(saveFileDialog.FileName + "\n\n导出完毕！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
    }
}
#if false // 反编译日志
缓存中的 75 项
------------------
解析: "mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
找到单个程序集: "mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
从以下位置加载: "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\mscorlib.dll"
------------------
解析: "System.Data, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
找到单个程序集: "System.Data, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
从以下位置加载: "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Data.dll"
------------------
解析: "System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
找到单个程序集: "System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
从以下位置加载: "C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8\System.Windows.Forms.dll"
------------------
解析: "GemBox.ExcelLite, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null"
找到单个程序集: "GemBox.ExcelLite, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null"
从以下位置加载: "G:\project\1.立中百检_内存泄漏\DLL\GemBox.ExcelLite.dll"
#endif
