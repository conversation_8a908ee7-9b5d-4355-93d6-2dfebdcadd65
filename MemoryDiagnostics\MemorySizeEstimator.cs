using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;

namespace MemoryDiagnostics
{
    public sealed class MemorySizeEstimator
    {
        // 近似常量（x64）
        private const int ObjectHeaderSize = 16; // 对象头开销（估算）
        private const int ReferenceSize = 8;     // 引用大小
        private const int ArrayHeaderSize = 24;  // 数组头开销（含长度、类型等）

        public long Estimate(object obj, MemoryEstimationContext ctx)
        {
            if (obj == null) return 0;
            if (ctx.Visited.Contains(obj)) return 0;
            ctx.Visited.Add(obj);

            // IMemoryMeasurable 优先
            if (obj is IMemoryMeasurable measurable)
            {
                return measurable.EstimateSize(this, ctx);
            }

            // 控件处理：默认跳过
            if (obj is Control)
            {
                if (!ctx.IncludeControls) return 0;
                // 仅粗略计对象头（Opaque 模式）
                return ObjectHeaderSize;
            }

            var type = obj.GetType();

            // 常见类型快速路径
            if (obj is string s) return EstimateOfString(s);
            if (obj is Array arr) return EstimateOfArray(arr, ctx);
            if (obj is IList list) return EstimateOfList(list, ctx);
            if (obj is IDictionary dict) return EstimateOfDictionary(dict, ctx);
            if (obj is StringBuilder sb) return EstimateOfStringBuilder(sb);
            if (obj is Bitmap bmp) return EstimateOfBitmap(bmp);

            // 值类型：按字段估算
            if (type.IsValueType)
            {
                return EstimateOfValueType(obj, ctx);
            }

            // 引用类型：只估算第一层字段（受深度控制）
            if (ctx.CurrentDepth >= ctx.MaxDepth)
            {
                return ctx.CountObjectHeader ? ObjectHeaderSize : 0;
            }

            long size = ctx.CountObjectHeader ? ObjectHeaderSize : 0;
            ctx.CurrentDepth++;
            try
            {
                foreach (var f in GetInstanceFields(type))
                {
                    object val = null;
                    try { val = f.GetValue(obj); } catch { }
                    if (val == null) continue;

                    if (f.FieldType.IsValueType)
                    {
                        size += EstimateOfValueType(val, ctx);
                    }
                    else
                    {
                        // 引用类型计引用大小，不深入或按深度规则估算
                        size += ReferenceSize;
                        size += Estimate(val, ctx);
                    }
                }
            }
            finally
            {
                ctx.CurrentDepth--;
            }

            return Align8(size);
        }

        private static IEnumerable<FieldInfo> GetInstanceFields(Type type)
        {
            const BindingFlags flags = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.DeclaredOnly;
            while (type != null && type != typeof(object))
            {
                foreach (var f in type.GetFields(flags))
                {
                    if (!f.IsStatic)
                        yield return f;
                }
                type = type.BaseType;
                break; // 只取第一层（DeclaredOnly 级别）
            }
        }

        public long EstimateOfString(string s)
        {
            if (s == null) return 0;
            return Align8(ObjectHeaderSize + ArrayHeaderSize + (long)s.Length * sizeof(char));
        }

        public long EstimateOfArray(Array arr, MemoryEstimationContext ctx)
        {
            if (arr == null) return 0;
            long size = ObjectHeaderSize + ArrayHeaderSize;
            var elemType = arr.GetType().GetElementType();
            int len = arr.Length;
            if (elemType.IsValueType)
            {
                size += (long)len * GetApproxValueTypeSize(elemType);
            }
            else
            {
                // 引用数组：引用大小，不深入元素（由深度控制进一步深入）
                size += (long)len * ReferenceSize;
                if (ctx.CurrentDepth < ctx.MaxDepth)
                {
                    ctx.CurrentDepth++;
                    try
                    {
                        foreach (var item in arr)
                        {
                            if (item != null)
                                size += Estimate(item, ctx);
                        }
                    }
                    finally { ctx.CurrentDepth--; }
                }
            }
            return Align8(size);
        }

        public long EstimateOfList(IList list, MemoryEstimationContext ctx)
        {
            if (list == null) return 0;
            long size = ObjectHeaderSize; // List 对象头
            Type listType = list.GetType();
            var elemType = listType.IsGenericType ? listType.GetGenericArguments()[0] : typeof(object);

            // 内部数组容量近似：尝试读取 _items 字段
            var itemsField = listType.GetField("_items", BindingFlags.Instance | BindingFlags.NonPublic);
            if (itemsField != null)
            {
                var arr = itemsField.GetValue(list) as Array;
                if (arr != null)
                {
                    size += EstimateOfArray(arr, ctx);
                }
            }
            else
            {
                // 兜底：按当前 Count 估算引用存储
                size += ArrayHeaderSize + (long)list.Count * (elemType.IsValueType ? GetApproxValueTypeSize(elemType) : ReferenceSize);
            }

            if (ctx.CurrentDepth < ctx.MaxDepth)
            {
                ctx.CurrentDepth++;
                try
                {
                    foreach (var item in list)
                    {
                        if (item != null)
                        {
                            if (elemType.IsValueType)
                                size += EstimateOfValueType(item, ctx);
                            else
                                size += Estimate(item, ctx);
                        }
                    }
                }
                finally { ctx.CurrentDepth--; }
            }

            return Align8(size);
        }

        public long EstimateOfDictionary(IDictionary dict, MemoryEstimationContext ctx)
        {
            if (dict == null) return 0;
            long size = ObjectHeaderSize;
            // 粗估 buckets 与 entries：按 Count 倍数近似
            int count = dict.Count;
            size += ArrayHeaderSize + (long)Math.Max(4, count * 2) * ReferenceSize; // buckets 近似
            size += ArrayHeaderSize + (long)count * (ReferenceSize * 3 + 8); // entries 近似（key、value、next/hashcode）

            if (ctx.CurrentDepth < ctx.MaxDepth)
            {
                ctx.CurrentDepth++;
                try
                {
                    foreach (DictionaryEntry kv in dict)
                    {
                        size += Estimate(kv.Key, ctx);
                        size += Estimate(kv.Value, ctx);
                    }
                }
                finally { ctx.CurrentDepth--; }
            }
            return Align8(size);
        }

        public long EstimateOfStringBuilder(StringBuilder sb)
        {
            if (sb == null) return 0;
            // 通过 Capacity 近似
            return Align8(ObjectHeaderSize + ArrayHeaderSize + (long)sb.Capacity * sizeof(char));
        }

        public long EstimateOfBitmap(Bitmap bmp)
        {
            if (bmp == null) return 0;
            // 近似：Width * Height * 4（按32bpp）+ 对象头
            long payload = (long)bmp.Width * bmp.Height * 4;
            return Align8(ObjectHeaderSize + payload);
        }

        public long EstimateOfValueType(object valueTypeInstance, MemoryEstimationContext ctx)
        {
            var t = valueTypeInstance.GetType();
            return Align8(GetApproxValueTypeSize(t));
        }

        private static int GetApproxValueTypeSize(Type t)
        {
            // 常见值类型近似大小
            if (t == typeof(bool)) return 1;
            if (t == typeof(byte) || t == typeof(sbyte)) return 1;
            if (t == typeof(short) || t == typeof(ushort)) return 2;
            if (t == typeof(char)) return 2;
            if (t == typeof(int) || t == typeof(uint)) return 4;
            if (t == typeof(float)) return 4;
            if (t == typeof(long) || t == typeof(ulong)) return 8;
            if (t == typeof(double)) return 8;
            if (t == typeof(decimal)) return 16;
            // 其它 struct 按字段累加（这里简化为 16 的倍数近似）
            return 16;
        }

        private static long Align8(long size)
        {
            long rem = size % 8;
            return rem == 0 ? size : (size + (8 - rem));
        }
    }
}