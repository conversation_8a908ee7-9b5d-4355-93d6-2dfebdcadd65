using System;
using System.Globalization;
using System.IO;
using System.Text;

namespace MemoryDiagnostics
{
    internal sealed class MemoryLogWriter
    {
        private readonly string _dir;
        public MemoryLogWriter(string dir)
        {
            _dir = dir;
            Directory.CreateDirectory(_dir);
        }

        private string GetFilePath(DateTime ts)
        {
            string name = $"Memory_{ts:yyyyMMdd}.csv";
            return Path.Combine(_dir, name);
        }

        public void AppendHeaderIfNeeded(DateTime ts)
        {
            string path = GetFilePath(ts);
            if (!File.Exists(path))
            {
                File.AppendAllText(path, "Timestamp,ObjectPath,ObjectType,EstimatedBytes,ItemCount,Notes,GC_TotalMemory,GC_Gen0,GC_Gen1,GC_Gen2\r\n", Encoding.UTF8);
            }
        }

        public void AppendLine(DateTime ts, string objectPath, string objectType, long estimatedBytes, long itemCount, string notes, long gcTotal, int gen0, int gen1, int gen2)
        {
            string path = GetFilePath(ts);
            string line = string.Join(",", new string[]
            {
                ts.ToString("yyyy-MM-dd HH:mm:ss"),
                Escape(objectPath),
                Escape(objectType),
                estimatedBytes.ToString(CultureInfo.InvariantCulture),
                itemCount.ToString(CultureInfo.InvariantCulture),
                Escape(notes ?? string.Empty),
                gcTotal.ToString(CultureInfo.InvariantCulture),
                gen0.ToString(CultureInfo.InvariantCulture),
                gen1.ToString(CultureInfo.InvariantCulture),
                gen2.ToString(CultureInfo.InvariantCulture)
            });
            File.AppendAllText(path, line + "\r\n", Encoding.UTF8);
        }

        private static string Escape(string s)
        {
            if (s == null) return string.Empty;
            if (s.Contains(",") || s.Contains("\"") || s.Contains("\n") || s.Contains("\r"))
            {
                s = s.Replace("\"", "\"\"");
                return "\"" + s + "\"";
            }
            return s;
        }
    }
}