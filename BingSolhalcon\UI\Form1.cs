using System;
using System.IO;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using HalconDotNet;
using System.Threading;
using System.Collections.Concurrent;
using BingSolhalcon.UI;
using System.Reflection;//引用反射集
using MvStereoAppSDKNet;

using DevExpress.XtraBars.Ribbon;
using DevExpress.LookAndFeel;

using Microsoft.Win32;
using BingSolhalcon.resources;
using System.Data.SqlClient;
using MemoryDiagnostics;

namespace BingSolhalcon
{
    //获取PLC消息的结构体
    public struct ReadPlcData
    {
        public bool get_wheeltype, get_lasermovval, get_deepval, get_cenholmovval, cenholetrigger, 
            get_hatholmoveval, hatholetrigger, get_markval, get_datamove, wheeltrigger,lasertrigger, 
            get_pretreatmentval, get_thicknessmoveval,get_writewheeltype,get_thinknessresult,get_thinknessswitch;
        public bool set_boltholedata;
        public double deepval1, deepval2;
        public string wheeltype;
        
    }




    public partial class Form1 : RibbonForm
    {
        //窗口缩放用变量
        private float x;//定义当前窗体的宽度
        private float y;//定义当前窗体的高度
                        //相机的变量
                        // BaslerCam mCamera1;//巴斯勒相机1
        HKCamera m_HKCamera1, m_HKCamera2, m_HKCamera3;//1、轮型识别2、中心孔检测3、帽止口检测
        HKCameraLink m_HKCameraLink1;
        HKVision_3D m_HKVision_3D1;
        static private List<string> m_DeviceList;//存枚举2D的相机名称
        static private List<string> m_DeviceList_3D;//存枚举3D的相机名称


        static private List<string> m_InterfaceList;//存枚举的采集卡
        static private List<string> m_CameraLinkDeviceList;//存枚举的cameralink相机名称

        string m_ip;
        int m_Rack, m_Slot;


        //系统用变量
        Dictionary<string, string> m_dictionary;//轮毂型号映射表用字典
        Dictionary<string, string> m_selectcolor;//颜色选择字典

        //plc用变量
        bool m_plcstatus = false;
        PcComPlc m_mainpccomplc, m_mainpccomplc1, m_mainpccomplc2, m_mainpccomplc3, m_mainpccomplc4;
        int m_readdb, m_writedb;

        long count1 = 0, c1_count = 1, c2_count = 1, c3_count = 1, c4_count = 1, c1_ngcount = 1, c2_ngcount = 1, c3_ngcount = 1, c4_ngcount = 1;

        //halcon用变量
        private HObject hImageSave1;
        private HObject ho_Image;
        //private static ConcurrentQueue<PassData> ConQueue_PassData;
        //private static List<PassData> ConQueue_PassData;
        private static List<Tuple<PassData, int>> ConQueue_PassData;

        private static volatile bool isenqueue = false;

        //winform control variables 

        private int TotalCountC1 = 0, TotalCountC2 = 0, TotalCountC3 = 0, TotalCountC4 = 0;
        private bool createstatus1 = false, createstatus2 = false, createstatus3 = false, createstatus4 = false;//相机是否实打开
        private bool m_databaseconnect = false;//数据库是否连接上
        private int historyindex = 0;

        private int booknum = 0;
        private Queue<string> m_message;//消息队列
        private const int SAVENUM = 90, MESSAGENUM = 12;//保存数据上限

        private bool savedatatpc = false, savetopcstatus = false;
        private bool ngsavetopc = false, ngsavetopcstatus = false;
        private Thread runtask;//离线测试线程
        private bool runtaskstatus = false;//离线测试状态变量
        private Datainteraction sendDatainter = new Datainteraction();
        private Datainteraction receiveDatainter = new Datainteraction();
        private SoftReg softReg;

        private int databasemaxindex = 0;//数据库最大序号
        private List<string> m_selectCheckListbox;//CheckListBox中选择的项目
        private string[] m_typenamerecord = { "nowrecord", "lastrecord" };//前、后两轮毂的记录
        public static string m_username;
        private string constr;//连接数据库用字句
        private string file = null, filepath = null, C1saveimagepath = null, C2saveimagepath = null, C3saveimagepath = null, C4saveimagepath = null, m_wheelcolor = null;
        private bool m_btnState, m_btndatabaseState;
        private bool[] m_c1btnstatus, m_c2btnstatus, m_c3btnstatus, m_c4btnstatus;

        private static ConcurrentQueue<SearchPat.ResultData> _keyQueue;
        private static SemaphoreSlim _semaphore;//限制资源调用 - 改为类级别管理，避免重复创建


        CameraParaSet m_cameraparaset1;//相机参数设置窗口
        S7communication s7commun;//西门子通讯窗口

        //SQlFun m_sqlfun;//数据库类

        ComponentResourceManager res = new ComponentResourceManager(typeof(nonUIresx)); //自定义资源字段

        private Dictionary<string, Dictionary<string, double>> m_dbcache = new Dictionary<string, Dictionary<string, double>>();

        // PLC重连计数
        private int mPLCReonnCount = 0;

        // C1拍照计数
        private int C1CaptureCount = 0;


        //线激光用变量 戴卡2310RANG_ZMIN = 40000
        const int UM_PIX = 150;
        const int RANG_ZMAX = 60000, RANG_ZMIN = -20000, RANG_XMAX = 70000, RANG_XMIN = 0;

        private log4net.ILog logger = log4net.LogManager.GetLogger("logger");
        private log4net.Appender.MemoryAppender m_memAppender = new log4net.Appender.MemoryAppender();
        private Thread m_logThread;
        private bool m_closed = false;


        private double m_deepZ;
        private double m_deepX;
        private double m_centreZ;
        private double m_hatZ;
        private double m_thicknessZ;
        private double m_markZ;
        private int m_decimalnum;

        private double m_holeScale;
        private int m_hatFocal;

        private int m_reduceRow;
        private int m_reduceCol;
        private int m_reduceRadius;

        private int m_reduceRow_C3;
        private int m_reduceCol_C3;
        private int m_reduceRadius_C3;

        private bool m_MEGAPHASE = true;
        private bool m_cameralink = false;

        private bool m_posDegReverse = false;

        private int m_defExposureTimeCen = 0;
        private int m_defExposureTimeHat = 0;

        private bool m_sendNGPosition = false;
        private string m_queryDb = "MyDB";
        private string m_wheelParam = "";

        private const bool m_feature = true;
        
        private MemoryMonitor _monitor;//用于解决内存泄漏问题的临时内存监视器

        public Form1()
        {
            InitializeComponent();

            log4net.Config.BasicConfigurator.Configure(m_memAppender);
            m_logThread = new Thread(() =>
            {
                while (!m_closed)
                {
                    Thread.Sleep(500);
                    try
                    {
                        var events = m_memAppender.GetEvents();
                        if (events != null && events.Length > 0)
                        {
                            m_memAppender.Clear();
                            string log = "";
                            foreach (var ev in events)
                            {
                                log += ev.RenderedMessage + "\r\n";
                            }
                            Invoke(new Action(() =>
                            {
                                try
                                {
                                    if (textBox_message.Text.Length > 500)
                                    {
                                        int startIndex = log.Length + 1;
                                        if (startIndex > 50)
                                        {
                                            startIndex = 50;
                                        }
                                        textBox_message.Text = textBox_message.Text.Remove(0, textBox_message.Text.IndexOf('\n', startIndex));
                                    }
                                    textBox_message.AppendText(log);
                                    textBox_message.SelectionStart = textBox_message.Text.Length;
                                    textBox_message.ScrollToCaret();
                                }
                                catch (Exception e)
                                {
                                    logger.Error(e.Message);
                                }
                            }));
                        }
                    }
                    catch
                    {

                    }
                }
            });
            m_logThread.Start();

            //窗口缩放用
            x = this.Width;
            y = this.Height;
            setTag(this);

            //窗口缩放用




            //hWindowControlC1.Width = groupBox1.Width / 3;

            //hWindowControlC2.Width = groupBox1.Width / 3;
            //hWindowControlC3.Width = groupBox1.Width / 3;

            m_DeviceList = new List<string>();
            m_InterfaceList = new List<string>();
            m_CameraLinkDeviceList = new List<string>();

            m_c1btnstatus = new bool[4] { true, false, true, false };
            m_c2btnstatus = new bool[4] { true, false, true, false };
            m_c3btnstatus = new bool[4] { true, false, true, false };
            m_c4btnstatus = new bool[4] { true, false, true, false };
            Control.CheckForIllegalCrossThreadCalls = false;
            hImageSave1 = new HObject();
            ho_Image = new HObject();

            m_mainpccomplc = new PcComPlc();
            m_mainpccomplc1 = new PcComPlc();
            m_mainpccomplc2 = new PcComPlc();
            m_mainpccomplc3 = new PcComPlc();
            m_mainpccomplc4 = new PcComPlc();

            ConQueue_PassData = new List<Tuple<PassData, int>>();
            for (int i = 0; i < 5; i++)
            {
                PassData m_passdata = new PassData();
                ConQueue_PassData.Add(new Tuple<PassData, int>(m_passdata, i));
            }

            m_message = new Queue<string>();


            m_cameraparaset1 = new CameraParaSet();
            s7commun = new S7communication();

            m_dictionary = new Dictionary<string, string>();
            m_selectcolor = new Dictionary<string, string>();
            m_selectCheckListbox = new List<string>();
            // dlrun = new DLRun();
            // dlrun.read_model(Directory.GetCurrentDirectory() + "/model/model_best.hdl");

            softReg = new SoftReg();


            loadConfig();

            constr = @"Server=" + Edit_Databasename.EditValue + ";Database=MyDB;Trusted_Connection=SSPI";
        }

        private void loadConfig()
        {
            try
            {
                // 读配置文件
                if (File.Exists(Application.StartupPath + "\\config.ini"))
                {
                    m_ip = ConfigIni.GetIniKeyValue("通讯地址", "IP", "************", Application.StartupPath + "\\config.ini");
                    m_Rack = Convert.ToInt16(ConfigIni.GetIniKeyValue("通讯地址", "导轨", "0", Application.StartupPath + "\\config.ini"));
                    m_Slot = Convert.ToInt16(ConfigIni.GetIniKeyValue("通讯地址", "槽号", "1", Application.StartupPath + "\\config.ini"));
                    m_writedb = Convert.ToInt16(ConfigIni.GetIniKeyValue("通讯地址", "工控机发送块", "200", Application.StartupPath + "\\config.ini"));
                    m_readdb = Convert.ToInt16(ConfigIni.GetIniKeyValue("通讯地址", "工控机接收块", "201", Application.StartupPath + "\\config.ini"));

                    Edit_Databasename.EditValue = ConfigIni.GetIniKeyValue("通讯地址", "数据库名称", ".", Application.StartupPath + "\\config.ini");


                    m_deepZ = double.Parse(ConfigIni.GetIniKeyValue("通讯地址", "深度Z轴", "100", Application.StartupPath + "\\config.ini"));
                    m_deepX = double.Parse(ConfigIni.GetIniKeyValue("通讯地址", "深度X轴", "100", Application.StartupPath + "\\config.ini"));
                    m_centreZ = double.Parse(ConfigIni.GetIniKeyValue("通讯地址", "中心孔Z轴", "100", Application.StartupPath + "\\config.ini"));
                    m_hatZ = double.Parse(ConfigIni.GetIniKeyValue("通讯地址", "帽止口Z轴", "100", Application.StartupPath + "\\config.ini"));
                    m_thicknessZ = double.Parse(ConfigIni.GetIniKeyValue("通讯地址", "厚度Z轴", "100", Application.StartupPath + "\\config.ini"));
                    m_markZ = double.Parse(ConfigIni.GetIniKeyValue("通讯地址", "标记Z轴", "100", Application.StartupPath + "\\config.ini"));

                    m_decimalnum = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "有效数据位", "2", Application.StartupPath + "\\config.ini"));

                    m_holeScale = double.Parse(ConfigIni.GetIniKeyValue("参数配置", "中心孔比例", "0.027368337315", Application.StartupPath + "\\config.ini"));
                    m_hatFocal = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "帽止口焦距", "372", Application.StartupPath + "\\config.ini"));

                    m_reduceRow = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "ReduceRow", "3172", Application.StartupPath + "\\config.ini"));
                    m_reduceCol = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "ReduceCol", "4731", Application.StartupPath + "\\config.ini"));
                    m_reduceRadius = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "ReduceRadius", "1650", Application.StartupPath + "\\config.ini"));

                    m_reduceRow_C3 = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "ReduceRow_C3", "2564", Application.StartupPath + "\\config.ini"));
                    m_reduceCol_C3 = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "ReduceCol_C3", "2283", Application.StartupPath + "\\config.ini"));
                    m_reduceRadius_C3 = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "ReduceRadius_C3", "1892", Application.StartupPath + "\\config.ini"));

                    m_MEGAPHASE = ConfigIni.GetIniKeyValue("参数配置", "MEGAPHASE", "True", Application.StartupPath + "\\config.ini").Equals("True");
                    m_cameralink = ConfigIni.GetIniKeyValue("参数配置", "CameraLink", "True", Application.StartupPath + "\\config.ini").Equals("True");

                    m_posDegReverse = ConfigIni.GetIniKeyValue("参数配置", "位置度顺序翻转", "True", Application.StartupPath + "\\config.ini").Equals("True");

                    m_defExposureTimeCen = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "中心孔默认曝光时长", "140000", Application.StartupPath + "\\config.ini"));
                    m_defExposureTimeHat = int.Parse(ConfigIni.GetIniKeyValue("参数配置", "帽止口默认曝光时长", "140000", Application.StartupPath + "\\config.ini"));

                    m_sendNGPosition = ConfigIni.GetIniKeyValue("参数配置", "发送NG位置", "False", Application.StartupPath + "\\config.ini").Equals("True");
                    m_wheelParam = ConfigIni.GetIniKeyValue("参数配置", "轮毂参数表", "", Application.StartupPath + "\\config.ini");
                    m_queryDb = ConfigIni.GetIniKeyValue("参数配置", "数据源", "MyDB", Application.StartupPath + "\\config.ini");
                }
            }
            catch (Exception e)
            {
                logger.Error("配置加载失败\n" + e.Message + e.StackTrace);
                MessageBox.Show("配置加载失败\n" + e.Message);
            }
        }

        private void setTag(Control cons)
        {
            foreach (Control con in cons.Controls)
            {
                con.Tag = con.Width + ";" + con.Height + ";" + con.Left + ";" + con.Top + ";" + con.Font.Size;
                if (con.Controls.Count > 0)
                {
                    setTag(con);
                }
            }
        }

        private void setControls(float newx, float newy, Control cons)
        {
            //遍历窗体中的控件，重新设置控件的值
            foreach (Control con in cons.Controls)
            {
                //获取控件的Tag属性值，并分割后存储字符串数组
                if (con.Tag != null)
                {
                    string[] mytag = con.Tag.ToString().Split(new char[] { ';' });
                    //根据窗体缩放的比例确定控件的值
                    con.Width = Convert.ToInt32(System.Convert.ToSingle(mytag[0]) * newx);//宽度
                    con.Height = Convert.ToInt32(System.Convert.ToSingle(mytag[1]) * newy);//高度
                    con.Left = Convert.ToInt32(System.Convert.ToSingle(mytag[2]) * newx);//左边距
                    con.Top = Convert.ToInt32(System.Convert.ToSingle(mytag[3]) * newy);//顶边距
                    Single currentSize = System.Convert.ToSingle(mytag[4]) * newy;//字体大小
                    con.Font = new Font(con.Font.Name, currentSize, con.Font.Style, con.Font.Unit);
                    if (con.Controls.Count > 0)
                    {
                        setControls(newx, newy, con);
                    }
                }
            }
        }


        private void callButtonEvent(Button btn, string EventName)
        {
            //建立一个类型      
            Type t = typeof(Button);
            //参数对象      
            object[] p = new object[1];
            //产生方法      
            MethodInfo m = t.GetMethod(EventName, BindingFlags.NonPublic | BindingFlags.Instance);
            //参数赋值。传入函数      
            //获得参数资料  
            ParameterInfo[] para = m.GetParameters();
            //根据参数的名字，拿参数的空值。  
            p[0] = Type.GetType(para[0].ParameterType.BaseType.FullName).GetProperty("Empty");
            //调用      
            m.Invoke(btn, p);
            return;
        }


        private void Form1_Load(object sender, EventArgs e)
        {
            Directory.SetCurrentDirectory(Application.StartupPath);

            if (!Directory.Exists(Application.StartupPath + "\\model"))
            {
                Directory.CreateDirectory(Application.StartupPath + "\\model");
            }
            if (!Directory.Exists(Application.StartupPath + "\\model\\lasershm"))
            {
                Directory.CreateDirectory(Application.StartupPath + "\\model\\lasershm");
            }
            if (!Directory.Exists(Application.StartupPath + "\\model\\Measurement"))
            {
                Directory.CreateDirectory(Application.StartupPath + "\\model\\Measurement");
            }
            if (!Directory.Exists(Application.StartupPath + "\\model\\mtr"))
            {
                Directory.CreateDirectory(Application.StartupPath + "\\model\\mtr");
            }
            if (!Directory.Exists(Application.StartupPath + "\\model\\registerpicture"))
            {
                Directory.CreateDirectory(Application.StartupPath + "\\model\\registerpicture");
            }

            //加载默认语言
            //MultiLanguage multilanguage = new MultiLanguage();
            //multilanguage.LoadDefaultLanguage(this, typeof(Form1));

            SQlFun m_sqlfun = new SQlFun();
            // mCamera1 = new BaslerCam("C1");

            m_HKCamera1 = new HKCamera();
            m_HKCamera2 = new HKCamera();
            m_HKCameraLink1 = new HKCameraLink();
            m_HKCamera3 = new HKCamera();
            HKCamera.DeviceListAcq(ref m_DeviceList);//枚举海康2D相机



            //枚举采集卡
            string stifilepath = Application.StartupPath + "\\MvFGProducerCML.cti";
            HKCameraLink.EnumInterface(stifilepath, ref m_InterfaceList);
            if (m_InterfaceList.Count != 0)
            {
                HKCameraLink.DeviceListAcq(ref m_CameraLinkDeviceList, 0);
            }


            int count_camera = m_DeviceList.Count;
            int count_cameralink = m_CameraLinkDeviceList.Count;



            for (int i = 0; i < count_camera; i++)
            {
                //cbEnumCamera.Items.Add(m_DeviceList[i]);
                cbEnumCamera.Properties.Items.Add(m_DeviceList[i]);
            }

            for (int i = 0; i < count_cameralink; i++)
            {
                cbEnumCamera.Properties.Items.Add(m_CameraLinkDeviceList[i]);
            }
            cbEnumCamera.SelectedIndex = 0;



            //halcon窗口分别初始化
            this.Height = 900;

            hWindowControlC1.Left = 0;
            //hWindowControlC1.Top = 0;

            this.label_Camera1status.ItemAppearance.Normal.BackColor = Color.Red;
            this.label_Camera2status.ItemAppearance.Normal.BackColor = Color.Red;
            this.label_Camera3status.ItemAppearance.Normal.BackColor = Color.Red;
            this.label_Camera4status.ItemAppearance.Normal.BackColor = Color.Red;
            this.label_DBStatus.ItemAppearance.Normal.BackColor = Color.Red;
            this.Btm_DisConnect.Enabled = false;

            //自动连接相机

            int nRet1 = m_HKCamera1.Open_Camera("C1", m_DeviceList);
            if (nRet1 != -1)
            {
                m_HKCamera1.SetTriggerMode();
                m_HKCamera1.SetSoftTrigger();

                m_HKCamera1.eventProcessImage += processHImage1;
                m_HKCamera1.StartGrab();
                createstatus1 = true;
                this.label_Camera1status.ItemAppearance.Normal.BackColor = Color.Green;
                m_cameraparaset1.groupBox3.Enabled = true;
                Btm_DisConnect.Enabled = true;
                Btm_Connect.Enabled = false;

                m_c1btnstatus[0] = false;
                m_c1btnstatus[1] = true;
                logger.Info("C1_connected");

            }
            else
            {
                logger.Info("C1_disconnected");
                MessageBoxEX.Show("C1_disconnected", "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
            }


            if (m_cameralink)
            {
                int nRet2 = m_HKCameraLink1.Open_Camera("C2", m_CameraLinkDeviceList);
                if (nRet2 != -1)
                {
                    m_HKCameraLink1.SetTriggerMode();
                    m_HKCameraLink1.SetSoftTrigger();

                    m_HKCameraLink1.eventProcessImage += processHImage2;
                    m_HKCameraLink1.StartGrab();
                    createstatus2 = true;
                    this.label_Camera2status.ItemAppearance.Normal.BackColor = Color.Green;
                    m_cameraparaset1.groupBox6.Enabled = true;
                    Btm_DisConnect.Enabled = true;
                    Btm_Connect.Enabled = false;

                    m_c2btnstatus[0] = false;
                    m_c2btnstatus[1] = true;
                    logger.Info("C2_connected");

                }
                else
                {
                    logger.Info("C2_disconnected");
                    MessageBoxEX.Show("C2_disconnected", "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                }
            }
            else
            {
                int nRet2 = m_HKCamera2.Open_Camera("C2", m_DeviceList);
                if (nRet2 != -1)
                {
                    m_HKCamera2.SetTriggerMode();
                    m_HKCamera2.SetSoftTrigger();

                    m_HKCamera2.eventProcessImage += processHImage2;
                    m_HKCamera2.StartGrab();
                    createstatus2 = true;
                    this.label_Camera2status.ItemAppearance.Normal.BackColor = Color.Green;
                    m_cameraparaset1.groupBox6.Enabled = true;
                    Btm_DisConnect.Enabled = true;
                    Btm_Connect.Enabled = false;

                    m_c2btnstatus[0] = false;
                    m_c2btnstatus[1] = true;
                    logger.Info("C2_connected");

                }
                else
                {
                    logger.Info("C2_disconnected");
                    MessageBoxEX.Show("C2_disconnected", "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                }
            }


            int nRet3 = m_HKCamera3.Open_Camera("C3", m_DeviceList);

            if (nRet3 != -1)
            {

                m_HKCamera3.SetTriggerMode();
                m_HKCamera3.SetSoftTrigger();
                m_HKCamera3.eventProcessImage += processHImage3;
                m_HKCamera3.StartGrab();
                createstatus3 = true;
                this.label_Camera3status.ItemAppearance.Normal.BackColor = Color.Green;
                m_cameraparaset1.groupBox9.Enabled = true;
                Btm_DisConnect.Enabled = true;
                Btm_Connect.Enabled = false;

                m_c3btnstatus[0] = false;
                m_c3btnstatus[1] = true;

                logger.Info("C3_connected");

            }
            else
            {
                logger.Info("C3_disconnected");
                MessageBoxEX.Show("C3_disconnected", "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
            }


            m_HKVision_3D1 = new HKVision_3D();
            m_HKVision_3D1.DeviceListAcq(ref m_DeviceList_3D);//枚举海康3D相机
            int count_camera3d = m_DeviceList_3D.Count;
            for (int i = 0; i < count_camera3d; i++)
            {
                //cbEnumCamera.Items.Add(m_DeviceList[i]);
                if (!cbEnumCamera.Properties.Items.Contains(m_DeviceList_3D[i]))
                    cbEnumCamera.Properties.Items.Add(m_DeviceList_3D[i]);
            }

            int nRet4 = m_HKVision_3D1.Open_Camera("C4", m_DeviceList_3D);

            if (nRet4 != -1)
            {

                m_HKVision_3D1.SetTriggerMode();
                // m_HKVision_3D1.SetSoftTrigger();
                m_HKVision_3D1.eventProcessImage += processHImage4;
                int index = m_DeviceList_3D.IndexOf("C4");
                m_HKVision_3D1.StartGrab(index);
                createstatus4 = true;
                this.label_Camera4status.ItemAppearance.Normal.BackColor = Color.Green;
                m_cameraparaset1.groupBox12.Enabled = true;
                Btm_DisConnect.Enabled = true;
                Btm_Connect.Enabled = false;

                m_c4btnstatus[0] = false;
                m_c4btnstatus[1] = true;

                logger.Info("C4_connected");

            }
            else
            {
                logger.Info("C4_disconnected");
                MessageBoxEX.Show("C4_disconnected", "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
            }


            //自动连接PLC
            if (m_feature)
            {
                s7commun.evenGetPLCData += GetPlcData2;
            }
            else
            {
                s7commun.evenGetPLCData += GetPlcData;
            }
            s7commun.evenDisconnectPLC += DisConnectPlc;
            s7commun.TxtIP.Text = m_ip;
            s7commun.TxtRack.Text = m_Rack.ToString();
            s7commun.TxtSlot.Text = m_Slot.ToString();
            s7commun.tB_SendDB.Text = m_writedb.ToString();
            s7commun.tB_RecDB.Text = m_readdb.ToString();
            //调用Button1的onclick  
            callButtonEvent(s7commun.ConnectBtn, "OnClick");
            //自动连接PLC


            UserLookAndFeel.Default.SetSkinStyle("Office 2010 Blue");
            switch (m_username)
            {
                case "操作员":
                    Btm_SetPara.Enabled = false;
                    Btm_FindTemplate.Enabled = false;
                    btn_circlecaliper.Enabled = false;
                    btn_3dfun.Enabled = false;
                    ribbonPageGroup9.Enabled = false;
                    Edit_Databasename.Enabled = false;
                    Btm_ConnectDatabase.Enabled = false;
                    Btm_LoadRecipe.Enabled = false;
                    ribbonPageGroup4.Enabled = false;
                    Btm_Setting.Enabled = false;
                    break;
                case "技术员":


                    break;
                case "专家":
                    break;

                case "Operator":
                    Btm_SetPara.Enabled = false;
                    ribbonPageGroup1.Enabled = false;
                    ribbonPageGroup9.Enabled = false;
                    break;
                case "Technician":

                    break;
                case "Expert":
                    break;

                case "operador":
                    Btm_SetPara.Enabled = false;
                    ribbonPageGroup1.Enabled = false;
                    ribbonPageGroup9.Enabled = false;
                    break;
                case "técnico":

                    break;
                case "experto":
                    break;

            }

            // 设置心跳定时器按钮使能

            timer_systemtime.Enabled = true;


            //  Importconfig_CheckListBox(CListB_SelWheelType, "selectwheeeltype");//Checklistbox从配置表导入
            // CheckSelectInChecklistbox();//CheckListBox数据入List<string>

            //DictionaryAdd();//映射表导入字典
            //halcon相关参数设置
            WindowParaSet winparset = new WindowParaSet();
            winparset.set_display_font(hWindowControlC1.HalconWindow, 12, "mono", "true", "false");
            winparset.set_display_font(hWindowControlC2.HalconWindow, 12, "mono", "true", "false");
            winparset.set_display_font(hWindowControlC3.HalconWindow, 12, "mono", "true", "false");

            //检测数据库是否连接正常
            m_sqlfun.connection(constr);
            if (m_sqlfun.Sql_open())
            {
                this.label_DBStatus.ItemAppearance.Normal.BackColor = Color.Cyan;
                this.Btm_ConnectDatabase.ItemAppearance.Normal.BackColor = Color.Cyan;
                Btm_ConnectDatabase.Caption = res.GetString("database_discon");
                this.m_btndatabaseState = true;
                m_databaseconnect = true;

            }
            if(m_feature)
            {
                BuildDatabaseCache();
                new Thread(ActionExecThread).Start();
            }

            // 启动内存监控（监控根对象：this）
            var options = new MemoryMonitorOptions
            {
                SampleInterval = TimeSpan.FromSeconds(60),
                IncludeControls = false,
                MaxDepth = 1,
                LogDirectory = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "memlogs")
            };
            _monitor = new MemoryMonitor(this, options);
            _monitor.Start();

            logger.Info("loadding finish");

            //CheckRegistry();
        }

        private void CheckRegistry()
        {

            //注册用
            const string userRoot = "HKEY_CURRENT_USER";
            const string subkey = "RegistrySetValueExample";
            const string keyName = userRoot + "\\" + subkey;

            //判断软件是否注册
            RegistryKey retkey = Registry.CurrentUser.OpenSubKey("SOFTWARE", true).CreateSubKey("mySoftWare").CreateSubKey("Register.INI");
            var rnum = softReg.GetRNum();
            foreach (string strRNum in retkey.GetSubKeyNames())
            {
                if (strRNum == rnum)
                {
                    MessageBoxEX.Show(res.GetString("alreadyRe"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                    logger.Info("已注册");
                    return;
                }
            }
            MessageBoxEX.Show(res.GetString("testVer"), res.GetString("message"), MessageBoxButtons.OK, new string[] { res.GetString("sure") });

            Int32 tLong;    //已使用次数
            try
            {
                tLong = (Int32)Registry.GetValue(keyName, "UseTimes", 0);
                MessageBoxEX.Show(res.GetString("alreadytimes") + tLong + res.GetString("times"), res.GetString("message"), MessageBoxButtons.OK, new string[] { res.GetString("sure") });
            }
            catch
            {
                MessageBoxEX.Show(res.GetString("welcome"), res.GetString("message"), MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                Registry.SetValue(keyName, "UseTimes", 0, RegistryValueKind.DWord);
            }


            //判断是否可以继续试用
            tLong = (Int32)Registry.GetValue(keyName, "UseTimes", 0);
            if (tLong < 5)


            {
                int tTimes = tLong + 1;
                Registry.SetValue(keyName, "UseTimes", tTimes);
            }
            else
            {
                DialogResult result = MessageBoxEX.Show(res.GetString("mosttimes"), res.GetString("message"), MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                if (result == DialogResult.Yes)
                {

                    FormRegister.state = false; //设置软件状态为不可用

                }
                else
                {
                    Application.Exit();
                }
            }
            //注册用
        }

        // 计算相机数量统计
        private void computeGrabTime1(long time)
        {
            ++count1;
            label_CountC2.Caption = res.GetString("num") + count1.ToString();

        }

        //轮型识别相机
        private void processHImage1(HObject hImg)
        {
            logger.Info("C1 已拍照");
            SQlFun m_sqlfun = new SQlFun();


            double[] angle = new double[5];
            short wheelindex = 0;//轮毂映射序号
            float angleoffset = 0, perimeter = 0;//角度偏移
            string typename = "", score = "", usetime = "", t_writestatus = "", exceptionmes = "";//轮毂型号及三菱写入返回状态
            HTuple Angle = null;
            SearchPat m_searchPat = new SearchPat();
            //多线程并行用清空队列
            //for (int i = 0; i < ConQueue_ho_Image.Count; i++)
            //{
            //    HObject delecthimage;
            //    ConQueue_ho_Image.TryDequeue(out delecthimage);

            //}
            //ConQueue_ho_Image.Count();
            //taskenqueue = Task.Factory.StartNew(() => {
            //    ConQueue_ho_Image.Enqueue(hImg);

            //}

            //  );

            // taskenqueue.Wait();
            Datainteraction datainter = new Datainteraction();

            isenqueue = true;
            hWindowControlC1.HalconWindow.ClearWindow();
            HTuple ho_width = null, ho_height = null;
            HOperatorSet.GetImageSize(hImg, out ho_width, out ho_height);
            HOperatorSet.SetPart(hWindowControlC1.HalconWindow, 0, 0, ho_height, ho_width);
            ho_width.Dispose();
            ho_height.Dispose();

            //如果是彩色相机，宽度方向减2可去除由bayer转换引起的一个像素的黑边
            //hWindowControl1.HalconWindow.SetPart(0, 0, mCamera1.imageHeight, mCamera1.imageWidth - 2); 

            hWindowControlC1.HalconWindow.DispObj(hImg);
            /*保存图像BMP格式*/
            //图片保存
            TotalCountC1++;
            this.label_CountC1.Caption = res.GetString("num") + TotalCountC1.ToString();
            if (m_btnState && (Directory.Exists(C1saveimagepath)))
            {


                string tt = C1saveimagepath + "\\" + DateTime.Now.ToString("yyyy-MM-dd") + "_";

                HOperatorSet.WriteImage(hImg, "bmp", 0, tt + c1_count.ToString());
                if (DateTime.Now.Hour == 24)
                    c1_count = 1;
                else
                    c1_count++;


            }



            /****轮型识别及中心孔定位****************/
            /****************************************/
            HTuple hv_Angle = null, deg_angle = null;
            HObject ho_Contour, ho_ImageReduced;
            HObject ho_Circle, ho_Circle1, ho_RegionDifference;

            HOperatorSet.GenEmptyObj(out ho_Contour);
            HOperatorSet.GenEmptyObj(out ho_ImageReduced);
            HOperatorSet.GenEmptyObj(out ho_Circle);
            HOperatorSet.GenEmptyObj(out ho_Circle1);
            HOperatorSet.GenEmptyObj(out ho_RegionDifference);


            int int_attackers = Environment.ProcessorCount;



            _semaphore = new SemaphoreSlim(int_attackers - 1);
            try
            {

                m_sqlfun.connection(constr);
                m_sqlfun.Sql_open();
                /****找轮毂外径***/
                // Screeninghubsize(ConQueue_ho_Image.ElementAt(0), hWindowControl1, out hubsize, out centerRow, out centerColumn);
                //   wheelsize whlsz = Pixelconversion(hubsize);

                //深度学习轮型识别部分
                // dlrun.inference(hWindowControlC1, hImg, out typename, out score);
                //传统算法轮型识别
                m_searchPat.SearchPattem(hImg, hWindowControlC1, _semaphore, _keyQueue, out typename, out score, out usetime);

                // 子母轮，对比轮毂高度区分轮型
                if (File.Exists(Directory.GetCurrentDirectory() + "\\model\\" + typename + ".zml"))
                {
                    double meavalue = m_mainpccomplc1.GetFloat(m_readdb, 14);

                    string hdtxt = File.ReadAllText(Directory.GetCurrentDirectory() + "\\model\\" + typename + ".hd");
                    var lines = hdtxt.Split('\n');
                    foreach (var line in lines)
                    {
                        double height;
                        m_sqlfun.Sql_Find("总高", "轮毂参数", line, out height);

                        if (Math.Abs(meavalue - height) < 20)
                        {
                            typename = line;
                        }
                    }
                }


                if (typename == "05017C08" || typename == "00619C69")
                    Select_SimilarWheel(ref typename);
                double t_score = double.Parse(score);
                if (t_score >= 0.6)
                {
                    string dispstr = "";

                    if (score.Length < 5)
                    {
                        dispstr = typename + "  " + score + dispstr;
                    }
                    else
                    {
                        dispstr = typename + "  " + score.Substring(0, 4) + dispstr;
                    }

                    HOperatorSet.DispText(hWindowControlC1.HalconWindow, dispstr, "window", "top", "left", "green", "box_color", "white");
                }
                else
                {
                    typename = "NG";
                    if (score.Length < 5)
                        HOperatorSet.DispText(hWindowControlC1.HalconWindow, typename + "  " + score, "window", "top",
                                           "left", "red", "box", "false");
                    else
                        HOperatorSet.DispText(hWindowControlC1.HalconWindow, typename + "  " + score.Substring(0, 4), "window", "top",
                                                               "left", "red", "box", "false");

                    //ng保存

                    string ngpath = "D:\\C1NG";
                    if (!Directory.Exists(ngpath))     // 返回bool类型，存在返回true，不存在返回false
                    {
                        Directory.CreateDirectory(ngpath);      //不存在则创建路径
                    }
                    else
                    {

                        HOperatorSet.WriteImage(hImg, "bmp", 0, ngpath + "\\" + DateTime.Now.ToString("yyyy-MM-dd") + "_" + c1_ngcount.ToString());
                        if (DateTime.Now.Hour == 24)
                            c1_ngcount = 1;
                        else
                            c1_ngcount++;
                    }

                }





                //轮毂型号对应的映射号

                if (m_sqlfun.conn.State == ConnectionState.Open)
                {
                    bool exist = m_sqlfun.Sql_ExistColumn("轮毂参数", "轮毂型号", typename);
                    {
                        double regionsize;
                        m_sqlfun.Sql_Find("序号", "轮毂参数", typename, out regionsize);
                        HOperatorSet.DispText(hWindowControlC1.HalconWindow, regionsize.ToString(), "window", 100,
                        50, "green", "box", "false");
                    }


                }
                // m_sqlfun.conn.Close();

                #region 找气门孔
                //找气门孔角度
                if (!string.IsNullOrEmpty(typename) && (typename != "NG"))

                {


                    // m_sqlfun.connection(constr);
                    // m_sqlfun.Sql_open();
                    if (m_sqlfun.conn.State == ConnectionState.Open)
                    {
                        bool exist = m_sqlfun.Sql_ExistColumn("气门孔范围直径", "轮毂型号", typename);

                        if (exist)
                        {
                            double regionsize;
                            m_sqlfun.Sql_Find("半径", "气门孔范围直径", typename, out regionsize);
                            //hubsize[0] = regionsize;
                            int res = Findsmallcircle(hImg, typename, regionsize, hWindowControlC1, out Angle);

                            HOperatorSet.TupleDeg(Angle, out deg_angle);
                            angle = deg_angle.ToDArr();
                            HOperatorSet.DispText(hWindowControlC1.HalconWindow, angle, "window", 50, 50, "green", "box", "false");

                            Angle.Dispose();
                            deg_angle.Dispose();
                        }


                        else
                        {

                            logger.Info("未录入此款轮毂的找孔区域");
                        }


                    }
                    //m_sqlfun.conn.Close();

                }
                else
                {

                    angle[0] = 0.0;
                }
                #endregion

            }
            catch (Exception ex)
            {
                logger.Info(ex.Message + ex.StackTrace);

                exceptionmes = ex.ToString().Substring(0, 100);
                HOperatorSet.DispText(hWindowControlC1.HalconWindow, "NG", "window", "top",
                    "left", "red", "box", "false");

                typename = "NG";
                m_wheelcolor = "0";
            }
            finally
            {

                try
                {
                    ho_Contour.Dispose();
                    ho_Circle.Dispose();
                    ho_Circle1.Dispose();
                    ho_RegionDifference.Dispose();
                    ho_ImageReduced.Dispose();

                    #region 通讯部分
                    //西门子通讯
                    int connect = m_mainpccomplc1.Connect(m_ip, m_Rack, m_Slot);
                    if (connect == 0)
                    {
                        if (m_sqlfun.conn.State == ConnectionState.Open)
                        {
                            bool exist = m_sqlfun.Sql_ExistColumn("轮毂参数", "轮毂型号", typename);
                            if (exist)
                            {
                                double regionsize1, regionsize2, regionsize3;
                                m_sqlfun.Sql_Find("序号", "轮毂参数", typename, out regionsize1);
                                m_sqlfun.Sql_Find("偏移角度", "轮毂参数", typename, out regionsize2);
                                m_sqlfun.Sql_Find("轮毂周长", "轮毂参数", typename, out regionsize3);
                                wheelindex = (short)regionsize1;
                                angleoffset = (float)regionsize2;
                                perimeter = (float)regionsize3;
                            }
                        }

                        m_mainpccomplc1.SetString(m_writedb, 42, typename);//写入轮毂型号
                        m_mainpccomplc1.SetFloat(m_writedb, 26, (float)angle[0]);//写入旋转角度
                        m_mainpccomplc1.SetFloat(m_writedb, 30, angleoffset);//写入偏移角度
                        m_mainpccomplc1.SetFloat(m_writedb, 34, perimeter);//写入轮毂周长
                        m_mainpccomplc1.SetInt(m_writedb, 38, wheelindex);//写入轮毂映射序号
                        m_mainpccomplc1.SetInt(m_writedb, 40, 666);//写入数据更新
                        m_mainpccomplc1.SetBit(m_writedb, 24, 3, true);//处理完成

                        logger.Info("写轮毂型号 [" + typename + "]");


                    }
                    else
                    {
                        logger.Info("PLC通讯失败");
                    }
                    #endregion

                    m_sqlfun.conn.Close();
                    _semaphore.Dispose();
                }
                catch (Exception e)
                {
                    logger.Error(e.Message + e.StackTrace);
                }

            }

        }

        //相机C2的函数
        //增添的最大实体原则与弦长测试函数
        void MeasureChordLength(boltholeresult boltholres, double holenum, out double[] resultval)
        {
            resultval = new double[8];
            for (int i = 0; i < holenum; i++)
            {
                double dis_x, dis_y;
                if (i + 1 < holenum)
                {
                    dis_x = boltholres.boltholecen[i].bolt_x - boltholres.boltholecen[i + 1].bolt_x;
                    dis_y = boltholres.boltholecen[i].bolt_y - boltholres.boltholecen[i + 1].bolt_y;
                }
                else
                {
                    dis_x = boltholres.boltholecen[i].bolt_x - boltholres.boltholecen[0].bolt_x;
                    dis_y = boltholres.boltholecen[i].bolt_y - boltholres.boltholecen[0].bolt_y;
                }
                resultval[i] = Math.Sqrt(dis_x * dis_x + dis_y * dis_y);
                string str = resultval[i].ToString("f2");
                resultval[i] = double.Parse(str);


            }


        }

        void ChordLength_CompareDesignVal(string databasepath, string wheeltype, double[] measureval, out double[] resultval, out string[] result)
        {
            SQlFun m_sqlfun = new SQlFun();
            m_sqlfun.connection(databasepath);
            m_sqlfun.Sql_open();
            string[] m_resule = new string[8];
            double[] m_resultval = new double[8];
            if (m_sqlfun.conn.State == ConnectionState.Open)
            {
                bool exist = m_sqlfun.Sql_ExistColumn("轮毂参数", "轮毂型号", wheeltype);

                if (exist)
                {
                    double m_designval, m_tolp, m_toln, top, bottom, m_holenum;


                    m_sqlfun.Sql_Find("弦长", "轮毂参数", wheeltype, out m_designval);
                    m_sqlfun.Sql_Find("弦长上公差", "轮毂参数", wheeltype, out m_tolp);
                    m_sqlfun.Sql_Find("弦长下公差", "轮毂参数", wheeltype, out m_toln);
                    m_sqlfun.Sql_Find("孔数", "轮毂参数", wheeltype, out m_holenum);
                    top = m_designval + m_tolp;
                    bottom = m_designval + m_toln;
                    for (int i = 0; i < m_holenum; i++)
                    {

                        if ((measureval[i] >= bottom) && (measureval[i] <= top))
                        {
                            m_resultval[i] = 0.0;
                            m_resule[i] = "OK";
                        }
                        else if (measureval[i] > top)
                        {
                            m_resule[i] = "NG";
                            m_resultval[i] = measureval[i] - top;


                        }
                        else if (measureval[i] < bottom)
                        {
                            m_resule[i] = "NG";
                            m_resultval[i] = measureval[i] - bottom;

                        }


                    }
                    resultval = m_resultval;
                    result = m_resule;

                }
                else
                {
                    for (int i = 0; i < 8; i++)
                    {
                        m_resultval[i] = 0.0;
                        m_resule[i] = "轮毂型号不存在";

                    }
                    resultval = m_resultval;
                    result = m_resule;
                }




            }
            else
            {
                for (int i = 0; i < 8; i++)
                {
                    m_resultval[i] = 0.0;
                    m_resule[i] = "数据库连接失败";

                }
                resultval = m_resultval;
                result = m_resule;

            }

            m_sqlfun.conn.Close();

        }

        void BoltholePosdeg_CompareDesignValMCC(string databasepath, string wheeltype, boltholeresult boltholres, double centerdia, out double[] resultval, out string[] result)
        {
            SQlFun m_sqlfun = new SQlFun();
            m_sqlfun.connection(databasepath);
            m_sqlfun.Sql_open();
            string[] m_resule = new string[8];
            double[] m_resultval = new double[8];
            if (m_sqlfun.conn.State == ConnectionState.Open)
            {
                bool exist = m_sqlfun.Sql_ExistColumn("轮毂参数", "轮毂型号", wheeltype);

                if (exist)
                {

                    double holenum;
                    double designval;
                    double boltdiadesignval;
                    double centerdiadesignval;
                    double cdut;
                    double cddt;
                    double hdut;
                    double hddt;

                    m_sqlfun.Sql_Find("孔数", "轮毂参数", wheeltype, out holenum);
                    m_sqlfun.Sql_Find("PCD位置度", "轮毂参数", wheeltype, out designval);
                    m_sqlfun.Sql_Find("螺栓孔直径", "轮毂参数", wheeltype, out boltdiadesignval);
                    m_sqlfun.Sql_Find("中心孔直径", "轮毂参数", wheeltype, out centerdiadesignval);
                    m_sqlfun.Sql_Find("中心孔直径上公差", "轮毂参数", wheeltype, out cdut);
                    m_sqlfun.Sql_Find("中心孔直径下公差", "轮毂参数", wheeltype, out cddt);
                    m_sqlfun.Sql_Find("螺栓孔直径上公差", "轮毂参数", wheeltype, out hdut);
                    m_sqlfun.Sql_Find("螺栓孔直径下公差", "轮毂参数", wheeltype, out hddt);

                    for (int i = 0; i < holenum; i++)
                    {
                        double compensation = boltholres.boltdiameter[i] - boltdiadesignval;
                        if (compensation <= 0)
                            compensation = 0;


                        if (boltholres.positondeg[i] < designval + centerdia - centerdiadesignval + boltholres.boltdiameter[i] - boltdiadesignval
                            && boltholres.positondeg[i] < (cdut - cddt) + (hdut - hddt) + designval)
                        {
                            m_resultval[i] = 0.0;
                            m_resule[i] = "OK";
                        }
                        else
                        {
                            m_resultval[i] = boltholres.positondeg[i] - designval - compensation;
                            m_resule[i] = "NG";
                        }
                    }
                    resultval = m_resultval;
                    result = m_resule;


                }
                else
                {
                    for (int i = 0; i < 8; i++)
                    {
                        m_resultval[i] = 0.0;
                        m_resule[i] = "轮毂型号不存在";

                    }
                    resultval = m_resultval;
                    result = m_resule;
                }
            }
            else
            {
                for (int i = 0; i < 8; i++)
                {
                    m_resultval[i] = 0.0;
                    m_resule[i] = "数据库连接失败";

                }
                resultval = m_resultval;
                result = m_resule;

            }
            m_sqlfun.conn.Close();

        }


        private void processHImage2(HObject hImg)
        {
            logger.Info("C2 已拍照 [" + ConQueue_PassData.ElementAt(3).Item2.ToString() + "," + ConQueue_PassData.ElementAt(3).Item1.wheeltype);
            SQlFun m_sqlfun = new SQlFun();
            string wheeltype = ConQueue_PassData.ElementAt(3).Item1.wheeltype;

            string m_mtrcensavepath = Directory.GetCurrentDirectory() + "\\model\\mtr\\" + wheeltype + "centrehole.mtr";
            string m_shmcensavepath = Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + wheeltype + "centre.shm";
            string m_mtrboltsavepath = Directory.GetCurrentDirectory() + "\\model\\mtr\\" + wheeltype + "bolthole.mtr";

            Caliper m_caliper = new Caliper();

            boltholeresult m_boltholeresult;

            HTuple ho_width = null, ho_height = null;
            HOperatorSet.GetImageSize(hImg, out ho_width, out ho_height);
            HOperatorSet.SetPart(hWindowControlC2.HalconWindow, 0, 0, ho_height, ho_width);
            HOperatorSet.DispObj(hImg, hWindowControlC2.HalconWindow);
            ho_width.Dispose();
            ho_height.Dispose();

            try
            {
                bool saveNgImg = true;
                double holenum, stdpcd, mmc_posdeg, chordlength; ;
                m_sqlfun.connection(constr);
                m_sqlfun.Sql_open();
                if (m_sqlfun.conn.State == ConnectionState.Open)
                {

                    bool exist = m_sqlfun.Sql_ExistColumn("中心孔位置度帽止口参数", "轮毂型号", wheeltype);
                    bool exist1 = m_sqlfun.Sql_ExistColumn("轮毂参数", "轮毂型号", wheeltype);

                    if (exist && exist1 && (File.Exists(m_mtrboltsavepath)))
                    {
                        m_sqlfun.Sql_Find("孔数", "轮毂参数", wheeltype, out holenum);
                        m_sqlfun.Sql_Find("PCD节圆直径", "轮毂参数", wheeltype, out stdpcd);

                        m_sqlfun.Sql_Find("最大实体原则开关", "轮毂参数", wheeltype, out mmc_posdeg);
                        m_sqlfun.Sql_Find("弦长测量开关", "轮毂参数", wheeltype, out chordlength);

                        double m_stdcenrow, m_stdcencolumn, m_stdboltrow, m_stdboltcolumn, m_stdboltradius, tem_boltratio;
                        double[] stdboltratio = new double[(int)holenum];
                        m_sqlfun.Sql_Find("中心孔Row", "中心孔位置度帽止口参数", wheeltype, out m_stdcenrow);
                        m_sqlfun.Sql_Find("中心孔Column", "中心孔位置度帽止口参数", wheeltype, out m_stdcencolumn);
                        m_sqlfun.Sql_Find("螺栓孔1Row", "中心孔位置度帽止口参数", wheeltype, out m_stdboltrow);
                        m_sqlfun.Sql_Find("螺栓孔1Column", "中心孔位置度帽止口参数", wheeltype, out m_stdboltcolumn);
                        m_sqlfun.Sql_Find("螺栓孔1Radius", "中心孔位置度帽止口参数", wheeltype, out m_stdboltradius);

                        for (int i = 0; i < holenum; i++)
                        {
                            m_sqlfun.Sql_Find("螺栓孔" + (i + 1).ToString() + "比例", "中心孔位置度帽止口参数", wheeltype, out tem_boltratio);
                            stdboltratio[i] = tem_boltratio;
                        }


                        double m_stdcenratio, m_diameter;

                        HTuple m_cenrow = new HTuple(), m_cencolumn = new HTuple(), m_pixdiameter = new HTuple();
                        m_sqlfun.Sql_Find("中心孔比例", "中心孔位置度帽止口参数", wheeltype, out m_stdcenratio);


                        // stdboltratio[0] = 0.0231538;//试用中心孔比例
                        int reduceRow = m_reduceRow;
                        int reduceCol = m_reduceCol;
                        int reduceRadius = m_reduceRadius;
                        m_caliper.MeasureCentreHole(m_mtrcensavepath, m_shmcensavepath, hImg, hWindowControlC2, m_stdcenratio, reduceRow, reduceCol, reduceRadius, m_decimalnum, out m_cenrow, out m_cencolumn, out m_pixdiameter, out m_diameter);
                        //中心孔跟理论值比较，得出结果
                        double resultval; string result;
                        m_caliper.CenHathole_CompareDesignVal(constr, wheeltype, "中心孔", m_diameter, false, out resultval, out result);


                        logger.Info("C2 " + m_diameter.ToString() + ", " + resultval.ToString() + ", " + result);

                        var boltHoleScale = m_holeScale;
                        m_caliper.MeasureBoltHole(hWindowControlC2, m_mtrboltsavepath, hImg, m_stdcenrow, m_stdcencolumn, stdpcd,
                            m_stdboltrow, m_stdboltcolumn, m_stdboltradius, (int)holenum, stdboltratio, m_cenrow, m_cencolumn, m_decimalnum, boltHoleScale, out m_boltholeresult);

                        m_cenrow.Dispose();
                        m_cencolumn.Dispose();
                        m_pixdiameter.Dispose();

                        if (m_posDegReverse)
                        {
                            int hnum = (int)holenum;
                            for (int i = 1; i <= (hnum - 1) / 2; ++i)
                            {
                                double t = m_boltholeresult.positondeg[i];
                                m_boltholeresult.positondeg[i] = m_boltholeresult.positondeg[hnum - i];
                                m_boltholeresult.positondeg[hnum - i] = t;
                            }
                        }
                        
                        for (int i = 0; i < holenum; i++)
                        {
                            if (m_boltholeresult.positondeg[i] != 0)
                            {
                                saveNgImg = false;
                                break;
                            }
                        }
                        if (m_diameter == 0 || result.Equals("NG") || m_boltholeresult.Equals("NG"))
                        {
                            saveNgImg = true;
                        }


                        //计算弦长
                        double[] resultval1 = new double[8]; string[] result1 = new string[8];
                        double[] chordlengthrel = new double[8];
                        if (chordlength == 2)
                        {

                            MeasureChordLength(m_boltholeresult, holenum, out chordlengthrel);
                            ChordLength_CompareDesignVal(constr, wheeltype, chordlengthrel, out resultval1, out result1);
                        }
                        else if (mmc_posdeg == 2)
                        {
                            BoltholePosdeg_CompareDesignValMCC(constr, wheeltype, m_boltholeresult, m_diameter, out resultval1, out result1);

                        }
                        else
                        {
                            //位置度跟理论值比较，得出结果
                            m_caliper.BoltholePosdeg_CompareDesignVal(constr, wheeltype, m_boltholeresult.positondeg, out resultval1, out result1);
                        }


                        //螺栓孔直径跟理论值比较，得出结果
                        double[] resultval2 = new double[8]; string[] result2 = new string[8];
                        m_caliper.Boltholediameter_CompareDesignVal(constr, wheeltype, m_boltholeresult.boltdiameter, out resultval2, out result2);


                        string dispstr = wheeltype + "   " + "中心孔尺寸:" + m_diameter.ToString() + "   " + result + "\r\n";


                        bool boltdiaNG = false;
                        for (int i = 0; i < holenum; i++)
                        {
                            if(m_boltholeresult.boltdiameter[i] == 0)
                            {
                                boltdiaNG = true;
                                break;
                            }
                        }
                        if (m_diameter == 0 || boltdiaNG)
                        {
                            for (int i = 0; i < holenum; i++)
                            {
                                result1[i] = "NG";
                            }
                        }


                            //屏幕窗口1显示结果
                            if (result == "OK")
                            HOperatorSet.DispText(hWindowControlC2.HalconWindow, dispstr, "window", 10,
                              10, "green", "box_color", "white");
                        else
                            HOperatorSet.DispText(hWindowControlC2.HalconWindow, dispstr, "window", 10,
                              10, "red", "box_color", "white");

                        dispstr = "帽槽深度:" + ConQueue_PassData[3].Item1.laserdeep_val[0].ToString() + "  " + ConQueue_PassData[3].Item1.deep_result;
                        if (ConQueue_PassData[3].Item1.deep_result == "OK")
                            HOperatorSet.DispText(hWindowControlC2.HalconWindow, dispstr, "window", 26,
                              10, "green", "box_color", "white");
                        else
                            HOperatorSet.DispText(hWindowControlC2.HalconWindow, dispstr, "window", 26,
                              10, "red", "box_color", "white");

                        for (int i = 0; i < holenum; i++)
                        {
                            if (chordlength == 2)
                                dispstr = "螺栓孔" + i.ToString() + "弦长:" + chordlengthrel[i].ToString() + " " + result1[i] +
                     " 直径" + m_boltholeresult.boltdiameter[i].ToString() + " " + result2[i];
                            else if (mmc_posdeg == 2)
                                dispstr = "螺栓孔" + i.ToString() + "MCC位置度:" + m_boltholeresult.positondeg[i].ToString() + " " + result1[i] +
                      " 直径" + m_boltholeresult.boltdiameter[i].ToString() + " " + result2[i];
                            else
                                dispstr = "螺栓孔" + i.ToString() + "位置度:" + m_boltholeresult.positondeg[i].ToString() + " " + result1[i] +
                          " 直径" + m_boltholeresult.boltdiameter[i].ToString() + " " + result2[i];


                            if ((result1[i] == "OK") && (result2[i] == "OK"))
                            {
                                HOperatorSet.DispText(hWindowControlC2.HalconWindow, dispstr, "window", 10 + 16 * (i + 2),
                                  10, "green", "box_color", "white");

                            }

                            else
                                HOperatorSet.DispText(hWindowControlC2.HalconWindow, dispstr, "window", 10 + 16 * (i + 2),
                                  10, "red", "box_color", "white");

                        }


                        //判定结果入队列
                        ConQueue_PassData[3].Item1.cen_val = m_diameter;
                        ConQueue_PassData[3].Item1.cencompareval = resultval;
                        ConQueue_PassData[3].Item1.cenhol_result = result;

                        ConQueue_PassData[3].Item1.position_val = m_boltholeresult.positondeg;
                        ConQueue_PassData[3].Item1.positioncompareval = resultval1;
                        ConQueue_PassData[3].Item1.pos_result = result1;

                        ConQueue_PassData[3].Item1.boltdiameter_val = m_boltholeresult.boltdiameter;
                        ConQueue_PassData[3].Item1.boltdiametercompareval = resultval2;
                        ConQueue_PassData[3].Item1.boltdiameter_result = result2;


                        //m_sqlfun.conn.Close();


                    }
                    else
                    {
                        logger.Warn("warning, " + res.GetString("param_hole"));
                        saveNgImg = true;
                    }

                    if (saveNgImg)
                    {
                        string ngpath = "D:\\C2NG";
                        if (!Directory.Exists(ngpath))     // 返回bool类型，存在返回true，不存在返回false
                        {
                            Directory.CreateDirectory(ngpath);      //不存在则创建路径
                        }
                        else
                        {
                            HOperatorSet.WriteImage(hImg, "bmp", 0, ngpath + "\\" + wheeltype + DateTime.Now.ToString("yyyy-MM-dd") + "_" + c2_ngcount.ToString());
                            if (DateTime.Now.Hour == 24)
                                c2_ngcount = 1;
                            else
                                c2_ngcount++;
                        }
                    }
                    // 调教位置
                    //m_caliper.CheckPosition(hWindowControlC2, hImg, 400000, 4465, 5719, 2797);
                }
                else
                {
                    throw new Exception("db connect failed");
                }
            }
            catch (Exception ex)
            {
                logger.Info(res.GetString("hole_dection") + ex.ToString());

                //ng保存
                try
                {
                    string ngpath = "D:\\C2NG";
                    if (!Directory.Exists(ngpath))     // 返回bool类型，存在返回true，不存在返回false
                    {
                        Directory.CreateDirectory(ngpath);      //不存在则创建路径
                    }
                    else
                    {
                        HOperatorSet.WriteImage(hImg, "bmp", 0, ngpath + "\\" + wheeltype + DateTime.Now.ToString("yyyy-MM-dd") + "_" + c2_ngcount.ToString());
                        if (DateTime.Now.Hour == 24)
                            c2_ngcount = 1;
                        else
                            c2_ngcount++;

                    }
                }
                catch (Exception ex2)
                {
                    logger.Info("save ng image failed \n" + ex2.ToString());
                }
            }
            finally
            {
                try
                {
                    int connect = m_mainpccomplc2.Connect(m_ip, m_Rack, m_Slot);
                    if (connect == 0)
                    {
                        m_mainpccomplc2.SetBit(m_writedb, 24, 0, true);
                    }
                    else
                    {
                        logger.Info(res.GetString("PLC_discommun"));
                    }

                    TotalCountC2++;
                    this.label_CountC2.Caption = res.GetString("num") + TotalCountC2.ToString();

                    //图片保存
                    if (m_btnState && (Directory.Exists(C2saveimagepath)))
                    {
                        string tt = C2saveimagepath + "\\" + DateTime.Now.ToString("yyyy-MM-dd") + "_";

                        HOperatorSet.WriteImage(hImg, "bmp", 0, tt + c2_count.ToString());

                        logger.Info("save C2 image: " + tt + ".bmp");

                        if (DateTime.Now.Hour == 24)
                            c2_count = 1;
                        else
                        {
                            //HTuple width = new HTuple(), height = new HTuple();
                            //HOperatorSet.GetImageSize(hImg, out width, out height);
                            //width.Dispose();
                            //height.Dispose();
                            c2_count++;
                        }
                    }
                    m_sqlfun.conn.Close();
                }
                catch (Exception ex)
                {
                    logger.Info("save image failed \n" + ex.ToString());
                }

            }
        }

        

        //相机C3的函数
        private void processHImage3(HObject hImg)
        {
            logger.Info("C3 已拍照 [" + ConQueue_PassData.ElementAt(2).Item2.ToString() + "," + ConQueue_PassData.ElementAt(2).Item1.wheeltype);
            SQlFun m_sqlfun = new SQlFun();
            string wheeltype = ConQueue_PassData.ElementAt(2).Item1.wheeltype;
            string m_mtrhatsavepath = Directory.GetCurrentDirectory() + "\\model\\mtr\\" + wheeltype + "hathole.mtr";
            string m_shmhatsavepath = Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + wheeltype + "hat.shm";

            Caliper m_caliper = new Caliper();

            HTuple ho_width = null, ho_height = null;
            HOperatorSet.GetImageSize(hImg, out ho_width, out ho_height);
            HOperatorSet.SetPart(hWindowControlC3.HalconWindow, 0, 0, ho_height, ho_width);
            HOperatorSet.DispObj(hImg, hWindowControlC3.HalconWindow);
            ho_width.Dispose();
            ho_height.Dispose();

            // 调教位置
            //m_caliper.CheckPosition(hWindowControlC3, hImg, 100000, 2500, 2500, 1200);
            try
            {
                m_sqlfun.connection(constr);
                m_sqlfun.Sql_open();
                if (m_sqlfun.conn.State == ConnectionState.Open)
                {
                    bool exist = m_sqlfun.Sql_ExistColumn("中心孔位置度帽止口参数", "轮毂型号", wheeltype);

                    if (exist)
                    {

                        double m_stdhatratio, m_diameter;
                        HTuple m_hatrow = new HTuple(), m_hatcolumn = new HTuple(), m_pixdiameter = new HTuple();


                        m_sqlfun.Sql_Find("帽止口比例", "中心孔位置度帽止口参数", wheeltype, out m_stdhatratio);
                        m_caliper.MeasureCentreHole(m_mtrhatsavepath, m_shmhatsavepath, hImg, hWindowControlC3, m_stdhatratio, m_reduceRow_C3, m_reduceCol_C3, m_reduceRadius_C3, m_decimalnum, out m_hatrow, out m_hatcolumn, out m_pixdiameter, out m_diameter);
                        //与理论值比较
                        double resultval; string result;
                        m_caliper.CenHathole_CompareDesignVal(constr, wheeltype, "帽止口", m_diameter, false, out resultval, out result);

                        m_hatrow.Dispose();
                        m_hatcolumn.Dispose();
                        m_pixdiameter.Dispose();


                        string dispstr = wheeltype + "   " + "帽止口尺寸:" + m_diameter.ToString() + "   " + result + "\r\n";

                        //屏幕窗口2显示结果

                        if (result == "OK")
                            HOperatorSet.DispText(hWindowControlC3.HalconWindow, dispstr, "window", 10,
                              10, "green", "box_color", "white");
                        else
                            HOperatorSet.DispText(hWindowControlC3.HalconWindow, dispstr, "window", 10,
                              10, "red", "box_color", "white");
                        dispstr = "  帽槽深度" + ConQueue_PassData[2].Item1.laserdeep_val[0].ToString() + "  " + ConQueue_PassData[2].Item1.deep_result;
                        if (ConQueue_PassData[2].Item1.deep_result == "OK")
                            HOperatorSet.DispText(hWindowControlC3.HalconWindow, dispstr, "window", 26,
                              10, "green", "box_color", "white");
                        else
                            HOperatorSet.DispText(hWindowControlC3.HalconWindow, dispstr, "window", 26,
                              10, "red", "box_color", "white");


                        //判定结果入队列
                        ConQueue_PassData[2].Item1.hat_val = m_diameter;
                        ConQueue_PassData[2].Item1.hatcompareval = resultval;
                        ConQueue_PassData[2].Item1.hathol_result = result;

                        //  m_sqlfun.conn.Close();

                    }
                    else
                    {
                        logger.Info(res.GetString("param"));

                        //if (m_message.Count > MESSAGENUM)
                        //    m_message.Dequeue();
                        //string str1 = "";
                        //foreach (string st in m_message)
                        //{
                        //    str1 = str1 + st + "\n";

                        //}
                        //textBox_message.Text = str1;
                    }


                }

                if(!ConQueue_PassData[2].Item1.hathol_result.Equals("OK"))
                {
                    string ngpath = "D:\\C3NG";
                    if (!Directory.Exists(ngpath))     // 返回bool类型，存在返回true，不存在返回false
                    {
                        Directory.CreateDirectory(ngpath);      //不存在则创建路径
                    }
                    else
                    {
                        HOperatorSet.WriteImage(hImg, "bmp", 0, ngpath + "\\" + wheeltype + DateTime.Now.ToString("yyyy-MM-dd") + "_" + c3_ngcount.ToString());
                        if (DateTime.Now.Hour == 24)
                            c3_ngcount = 1;
                        else
                            c3_ngcount++;
                    }
                }

            }
            catch (Exception ex)
            {

                //ng保存

                string ngpath = "D:\\C3NG";
                if (!Directory.Exists(ngpath))     // 返回bool类型，存在返回true，不存在返回false
                {
                    Directory.CreateDirectory(ngpath);      //不存在则创建路径

                }
                else
                {
                    HOperatorSet.WriteImage(hImg, "bmp", 0, ngpath + "\\" + wheeltype + DateTime.Now.ToString("yyyy-MM-dd") + "_" + c3_ngcount.ToString());
                    if (DateTime.Now.Hour == 24)
                        c3_ngcount = 1;
                    else
                        c3_ngcount++;

                }
                logger.Error(ex.Message + ex.StackTrace);
            }
            finally
            {

                int connect = m_mainpccomplc3.Connect(m_ip, m_Rack, m_Slot);
                if (connect == 0)
                {
                    m_mainpccomplc3.SetBit(m_writedb, 24, 1, true);
                    // m_mainpccomplc2.DisConnect();
                }
                else
                {
                    logger.Info(res.GetString("PLC_discommun"));

                }

                TotalCountC3++;
                this.label_CountC3.Caption = res.GetString("num") + TotalCountC3.ToString();
                //图片保存

                if (m_btnState && (Directory.Exists(C3saveimagepath)))
                {

                    string tt = C3saveimagepath + "\\" + DateTime.Now.ToString("yyyy-MM-dd") + "_";

                    HOperatorSet.WriteImage(hImg, "bmp", 0, tt + c3_count.ToString());
                    if (DateTime.Now.Hour == 24)
                        c3_count = 1;
                    else
                        c3_count++;


                }
                m_sqlfun.conn.Close();
            }


        }
        

        //3D线激光
        private void transform4(HTuple inObjectModel3D, out HTuple outObjectModel3D)
        {

            HTuple hv_original_x = new HTuple(), hv_original_y = new HTuple();
            HTuple hv_original_z = new HTuple();
            hv_original_x.Dispose();
            HOperatorSet.GetObjectModel3dParams(inObjectModel3D, "point_coord_x", out hv_original_x);
            hv_original_y.Dispose();
            HOperatorSet.GetObjectModel3dParams(inObjectModel3D, "point_coord_y", out hv_original_y);
            hv_original_z.Dispose();
            HOperatorSet.GetObjectModel3dParams(inObjectModel3D, "point_coord_z", out hv_original_z);
            using (HDevDisposeHelper dh = new HDevDisposeHelper())
            {
                {
                    HTuple
                      ExpTmpLocalVar_original_x = 2 * hv_original_x;
                    hv_original_x.Dispose();
                    hv_original_x = ExpTmpLocalVar_original_x;
                }
            }
            using (HDevDisposeHelper dh = new HDevDisposeHelper())
            {
                {
                    HTuple
                      ExpTmpLocalVar_original_y = 2 * hv_original_y;
                    hv_original_y.Dispose();
                    hv_original_y = ExpTmpLocalVar_original_y;
                }
            }
            using (HDevDisposeHelper dh = new HDevDisposeHelper())
            {
                {
                    HTuple
                      ExpTmpLocalVar_original_z = 2 * hv_original_z;
                    hv_original_z.Dispose();
                    hv_original_z = ExpTmpLocalVar_original_z;
                }
            }


            HOperatorSet.GenObjectModel3dFromPoints(hv_original_x, hv_original_y, hv_original_z,
                out outObjectModel3D);

            hv_original_x.Dispose();
            hv_original_y.Dispose();
            hv_original_z.Dispose();

        }

        private void processHImage4(MvStereoApp.STC_DATA_IMAGE hImg)
        {
            logger.Info("C4 已拍照");

            string wheeltype = ConQueue_PassData.ElementAt(4).Item1.wheeltype;
            // wheeltype = "00616C11";
            HTuple ho_ObjectModel3D = null;

            _3DFunc._3DFunc m_3dfun = new _3DFunc._3DFunc();


            //程序执行段

            string m_savepath = Directory.GetCurrentDirectory() + "\\model\\lasershm\\" + wheeltype + "laser.shm";


            HObject ho_IntersectionX;
            HObject ho_region, ho_image, ho_imagecleard, ho_imageresult, ho_rec1;

            HObject ho_CroppedContours, ho_ContourAngle, ho_Contour, ho_Contour1, ho_Contour2, ho_Contourl3, ho_Contourl4;
            HObject ho_ParallelContours;

            HOperatorSet.GenEmptyObj(out ho_CroppedContours);
            HOperatorSet.GenEmptyObj(out ho_ContourAngle);
            HOperatorSet.GenEmptyObj(out ho_Contour);
            HOperatorSet.GenEmptyObj(out ho_Contour1);
            HOperatorSet.GenEmptyObj(out ho_Contour2);
            HOperatorSet.GenEmptyObj(out ho_Contourl3);
            HOperatorSet.GenEmptyObj(out ho_Contourl4);

            HOperatorSet.GenEmptyObj(out ho_ParallelContours);
            HOperatorSet.GenEmptyObj(out ho_rec1);

            HTuple hv_ObjectModel3D1 = new HTuple(), hv_Width = new HTuple();
            HTuple hv_Height = new HTuple(), hv_WindowHandle = new HTuple();
            HTuple hv_BorderFact = new HTuple(), hv_IsTelecentric = new HTuple();
            HTuple hv_Pose = new HTuple(), hv_CamParam = new HTuple();
            HTuple hv_Labels = new HTuple(), hv_VisParamName = new HTuple();
            HTuple hv_row1 = new HTuple(), hv_col1 = new HTuple();
            HTuple hv_row2 = new HTuple(), hv_col2 = new HTuple();

            HTuple hv_rowtran1 = new HTuple(), hv_coltran1 = new HTuple();
            HTuple hv_rowtran2 = new HTuple(), hv_coltran2 = new HTuple();


            HTuple hv_Row0 = new HTuple(), hv_Col0 = new HTuple(), hv_Distance0 = new HTuple();



            HTuple hv_rowt = new HTuple(), hv_coll = new HTuple();
            HTuple hv_rowb = new HTuple(), hv_colr = new HTuple();

            HTuple hv_rowtrant = new HTuple(), hv_coltranl = new HTuple();
            HTuple hv_rowtranb = new HTuple(), hv_coltranr = new HTuple();

            HTuple hv_ModelID3 = new HTuple(), hv_CenterRow = new HTuple();
            HTuple hv_CenterColumn = new HTuple(), hv_CenterAngle = new HTuple();
            HTuple hv_CenterScore = new HTuple();

            HTuple hv_homMat2DIdentity = new HTuple();
            HTuple hv_homMat2DRotate = new HTuple();
            HTuple hv_HomMat2DTranslate = new HTuple();


            HTuple hv_FitRowBegin = new HTuple(), hv_FitColBegin = new HTuple();
            HTuple hv_FitRowEnd = new HTuple(), hv_FitColEnd = new HTuple();
            HTuple hv_Nr = new HTuple(), hv_Nc = new HTuple(), hv_Dist = new HTuple();
            HTuple hv_InterRow = new HTuple(), hv_InterColumn = new HTuple(), hv_IsOverlapping = new HTuple();
            HTuple hv_RowProj = new HTuple();
            HTuple hv_ColProj = new HTuple(), hv_OffsetDistance = new HTuple();
            HTuple hv_Length = new HTuple(), hv_RowProj1 = new HTuple();
            HTuple hv_ColProj1 = new HTuple(), hv_RowProj2 = new HTuple();
            HTuple hv_ColProj2 = new HTuple();

            HTuple hv_rang_zmax = new HTuple();
            HTuple hv_rang_zmin = new HTuple(), hv_rang_xmax = new HTuple();
            HTuple hv_rang_xmin = new HTuple(), hv_Thresholdedz = new HTuple();
            HTuple hv_Thresholdedx = new HTuple(), hv_GenParamValuez = new HTuple();
            HTuple hv_GenParamValuex = new HTuple(), hv_Max_z = new HTuple();
            HTuple hv_Max_x = new HTuple(), hv_Min_z = new HTuple();
            HTuple hv_Min_x = new HTuple(), hv_x_dis = new HTuple();
            HTuple hv_translate1 = new HTuple();
            HTuple hv_revolve1 = new HTuple(), hv_GenParamValuey = new HTuple();
            HTuple hv_Min_y = new HTuple(), hv_translate2 = new HTuple();
            HTuple hv_z_dis = new HTuple(), hv_scale_x = new HTuple();
            HTuple hv_scale_z = new HTuple(), hv_scale = new HTuple();

            //修改部分@1
            HTuple hv_CroppedRow = new HTuple(), hv_CroppedCol = new HTuple();

            HOperatorSet.GenEmptyObj(out ho_IntersectionX);
            HOperatorSet.GenEmptyObj(out ho_region);
            HOperatorSet.GenEmptyObj(out ho_image);
            HOperatorSet.GenEmptyObj(out ho_imagecleard);
            HOperatorSet.GenEmptyObj(out ho_imageresult);

            double[] m_parameter = new double[10];

            SQlFun m_sqlfun = new SQlFun();

            try
            {
                m_HKVision_3D1.PointCloudImage_halcon(hImg, out ho_ObjectModel3D);

                transform4(ho_ObjectModel3D, out ho_ObjectModel3D);

                hWindowControlC4.Invoke(new MethodInvoker(delegate
                {
                    hWindowControlC4.Enabled = false;


                }));
                hv_ObjectModel3D1.Dispose();
                HOperatorSet.ClearWindow(hWindowControlC4.HalconWindow);
                if (ho_ObjectModel3D.Length != 0)
                {
                    // m_3dfun.filterdata(ho_ObjectModel3D, -32768, out hv_ObjectModel3D1);
                    //HTuple pose = new HTuple();

                    //HOperatorSet.CreatePose(-0.02, 0.01, 200000, 90, 0, 0, "Rp+T", "gba", "point",
                    //            out pose);
                    //  m_3dfun.visualizeObject(this.hWindowControlC4.HalconWindow, hv_ObjectModel3D1, true, pose, "Green");



                    hv_rang_zmax.Dispose();
                    hv_rang_zmax = RANG_ZMAX;
                    hv_rang_zmin.Dispose();
                    hv_rang_zmin = RANG_ZMIN;
                    hv_rang_xmax.Dispose();
                    hv_rang_xmax = RANG_XMAX;
                    hv_rang_xmin.Dispose();
                    hv_rang_xmin = RANG_XMIN;

                    /////////////////////////////////
                    hv_Thresholdedz.Dispose();
                    HTuple num = new HTuple();

                    HOperatorSet.SelectPointsObjectModel3d(ho_ObjectModel3D, "point_coord_z",
                        hv_rang_zmin, hv_rang_zmax, out hv_Thresholdedz);

                    HOperatorSet.SelectPointsObjectModel3d(hv_Thresholdedz, "point_coord_x", hv_rang_xmin,
                        hv_rang_xmax, out hv_Thresholdedx);

                    //HOperatorSet.GetObjectModel3dParams(hv_Thresholdedx, "num_points", out num);
                    //MessageBox.Show(num.ToString());

                    hv_GenParamValuez.Dispose();
                    HOperatorSet.GetObjectModel3dParams(hv_Thresholdedx, "point_coord_z", out hv_GenParamValuez);
                    hv_GenParamValuex.Dispose();
                    HOperatorSet.GetObjectModel3dParams(hv_Thresholdedx, "point_coord_x", out hv_GenParamValuex);
                    hv_Max_z.Dispose();
                    HOperatorSet.TupleMax(hv_GenParamValuez, out hv_Max_z);
                    hv_Max_x.Dispose();
                    HOperatorSet.TupleMax(hv_GenParamValuex, out hv_Max_x);
                    hv_Min_z.Dispose();
                    HOperatorSet.TupleMin(hv_GenParamValuez, out hv_Min_z);
                    hv_Min_x.Dispose();
                    HOperatorSet.TupleMin(hv_GenParamValuex, out hv_Min_x);

                    HTuple
                      ExpTmpLocalVar_GenParamValuez = hv_GenParamValuez - hv_Min_z;
                    hv_GenParamValuez.Dispose();
                    hv_GenParamValuez = ExpTmpLocalVar_GenParamValuez;


                    HTuple
                      ExpTmpLocalVar_GenParamValuex = hv_GenParamValuex - hv_Min_x;
                    hv_GenParamValuex.Dispose();
                    hv_GenParamValuex = ExpTmpLocalVar_GenParamValuex;

                    hv_translate1.Dispose();
                    HOperatorSet.RigidTransObjectModel3d(hv_Thresholdedx, ((((((-hv_Min_x)).TupleConcat(
                        0))).TupleConcat(-hv_Min_z))).TupleConcat((((new HTuple(0)).TupleConcat(
                        0)).TupleConcat(0)).TupleConcat(0)), out hv_translate1);

                    hv_revolve1.Dispose();
                    HOperatorSet.RigidTransObjectModel3d(hv_translate1, ((((((new HTuple(0)).TupleConcat(
                        0)).TupleConcat(0)).TupleConcat(90)).TupleConcat(0)).TupleConcat(0)).TupleConcat(
                        0), out hv_revolve1);
                    hv_GenParamValuey.Dispose();
                    HOperatorSet.GetObjectModel3dParams(hv_revolve1, "point_coord_y", out hv_GenParamValuey);
                    hv_Min_y.Dispose();
                    HOperatorSet.TupleMin(hv_GenParamValuey, out hv_Min_y);

                    hv_translate2.Dispose();
                    HOperatorSet.RigidTransObjectModel3d(hv_revolve1, (((new HTuple(0)).TupleConcat(
                        -hv_Min_y))).TupleConcat(((((new HTuple(0)).TupleConcat(0)).TupleConcat(
                        0)).TupleConcat(0)).TupleConcat(0)), out hv_translate2);

                    //定义一个系数用于在3D模型中计算图像的边缘
                    hv_BorderFact.Dispose();
                    hv_BorderFact = 1.5;
                    //是否选择镜头.
                    hv_IsTelecentric.Dispose();
                    hv_IsTelecentric = 1;

                    //定义相机参数以及相机初始位姿.

                    hv_Pose.Dispose();
                    HOperatorSet.CreatePose(0, 0, 0, 0, 0, 0, "Rp+T", "gba", "point", out hv_Pose);
                    hv_CamParam.Dispose();
                    hv_CamParam = new HTuple();
                    hv_CamParam[0] = 0;
                    hv_CamParam[1] = 0;
                    hv_CamParam[2] = UM_PIX;
                    hv_CamParam[3] = UM_PIX;
                    hv_CamParam[4] = 0;
                    hv_CamParam[5] = 0;
                    hv_CamParam[6] = 500;
                    hv_CamParam[7] = 500;
                    ho_IntersectionX.Dispose();
                    HOperatorSet.ProjectObjectModel3d(out ho_IntersectionX, hv_translate2, hv_CamParam,
                        hv_Pose, (((new HTuple("data")).TupleConcat("point_shape")).TupleConcat(
                        "point_size")).TupleConcat("union_adjacent_contours"), (((new HTuple("auto")).TupleConcat(
                        "circle")).TupleConcat(1)).TupleConcat("true"));

                    ho_ContourAngle.Dispose();
                    m_3dfun.PointTransXld(ho_IntersectionX, out ho_ContourAngle);
                    HOperatorSet.SetPart(hWindowControlC4.HalconWindow, 0, 0, 500, 500);
                    HOperatorSet.SetColor(hWindowControlC4.HalconWindow, "cyan");
                    HOperatorSet.DispObj(ho_ContourAngle, hWindowControlC4.HalconWindow);

                    //   HOperatorSet.GenRegionContourXld(ho_ContourAngle, out ho_region, "filled");
                    HOperatorSet.GenImageConst(out ho_image, "byte", 500, 500);
                    HOperatorSet.GenImageProto(ho_image, out ho_imagecleard, 0);
                    HOperatorSet.PaintXld(ho_ContourAngle, ho_imagecleard, out ho_imageresult, 255);
                    // HOperatorSet.PaintRegion(ho_region, ho_imagecleard, out ho_imageresult, 255, "fill");




                    if (File.Exists(m_savepath))
                        HOperatorSet.ReadShapeModel(m_savepath, out hv_ModelID3);
                    HOperatorSet.FindShapeModel(ho_imageresult, hv_ModelID3, 0, (new HTuple(180)).TupleRad()
            , 0.5, 1, 0.5, "least_squares", 8, 0.9, out hv_CenterRow, out hv_CenterColumn, out hv_CenterAngle,
                     out hv_CenterScore);

                    HOperatorSet.HomMat2dIdentity(out hv_homMat2DIdentity);
                    HOperatorSet.HomMat2dRotate(hv_homMat2DIdentity, hv_CenterAngle, 0, 0, out hv_homMat2DRotate);
                    HOperatorSet.HomMat2dTranslate(hv_homMat2DRotate, hv_CenterRow, hv_CenterColumn, out hv_HomMat2DTranslate);




                    m_sqlfun.connection(constr);
                    m_sqlfun.Sql_open();
                    if (m_sqlfun.conn.State == ConnectionState.Open)
                    {
                        bool exist = m_sqlfun.Sql_ExistColumn("三维检测配方", "轮毂型号", wheeltype);

                        if (exist)
                        {
                            m_sqlfun.Sql_Find("矩形1Row1", "三维检测配方", wheeltype, out m_parameter[0]);
                            m_sqlfun.Sql_Find("矩形1Column1", "三维检测配方", wheeltype, out m_parameter[1]);
                            m_sqlfun.Sql_Find("矩形1Row2", "三维检测配方", wheeltype, out m_parameter[2]);
                            m_sqlfun.Sql_Find("矩形1Column2", "三维检测配方", wheeltype, out m_parameter[3]);
                            m_sqlfun.Sql_Find("矩形2Row1", "三维检测配方", wheeltype, out m_parameter[4]);
                            m_sqlfun.Sql_Find("矩形2Column1", "三维检测配方", wheeltype, out m_parameter[5]);
                            m_sqlfun.Sql_Find("矩形2Row2", "三维检测配方", wheeltype, out m_parameter[6]);
                            m_sqlfun.Sql_Find("矩形2Column2", "三维检测配方", wheeltype, out m_parameter[7]);
                            m_sqlfun.Sql_Find("测量点偏移", "三维检测配方", wheeltype, out m_parameter[8]);

                            m_sqlfun.conn.Close();


                            hv_row1[0] = m_parameter[0];
                            hv_col1[0] = m_parameter[1];
                            hv_row2[0] = m_parameter[2];
                            hv_col2[0] = m_parameter[3];

                            hv_rowt[0] = m_parameter[4];
                            hv_coll[0] = m_parameter[5];
                            hv_rowb[0] = m_parameter[6];
                            hv_colr[0] = m_parameter[7];

                            HOperatorSet.AffineTransPoint2d(hv_HomMat2DTranslate, hv_row1, hv_col1, out hv_rowtran1, out hv_coltran1);
                            HOperatorSet.AffineTransPoint2d(hv_HomMat2DTranslate, hv_row2, hv_col2, out hv_rowtran2, out hv_coltran2);
                            HOperatorSet.AffineTransPoint2d(hv_HomMat2DTranslate, hv_rowt, hv_coll, out hv_rowtrant, out hv_coltranl);
                            HOperatorSet.AffineTransPoint2d(hv_HomMat2DTranslate, hv_rowb, hv_colr, out hv_rowtranb, out hv_coltranr);

                            HOperatorSet.GenRectangle1(out ho_rec1, hv_rowtrant, hv_coltranl, hv_rowtranb, hv_coltranr);
                            //  HOperatorSet.GenRectangle1(out ho_rec1, hv_rowt, hv_coll, hv_rowb, hv_colr);
                            HOperatorSet.SetDraw(hWindowControlC4.HalconWindow, "margin");
                            HOperatorSet.SetColor(hWindowControlC4.HalconWindow, "yellow");
                            HOperatorSet.DispObj(ho_rec1, hWindowControlC4.HalconWindow);
                            ho_CroppedContours.Dispose();
                            HOperatorSet.CropContoursXld(ho_ContourAngle, out ho_CroppedContours, hv_rowtrant, hv_coltranl, hv_rowtranb, hv_coltranr, "true");
                            //   HOperatorSet.CropContoursXld(ho_ContourAngle, out ho_CroppedContours, hv_rowt, hv_coll, hv_rowb, hv_colr, "true");

                            //修改部分@2
                            hv_CroppedRow.Dispose(); hv_CroppedCol.Dispose();
                            HOperatorSet.GetContourXld(ho_CroppedContours, out hv_CroppedRow, out hv_CroppedCol);

                            hv_FitRowBegin.Dispose(); hv_FitColBegin.Dispose(); hv_FitRowEnd.Dispose(); hv_FitColEnd.Dispose(); hv_Nr.Dispose(); hv_Nc.Dispose(); hv_Dist.Dispose();


                            HOperatorSet.SetColor(hWindowControlC4.HalconWindow, "blue");

                            ho_Contour.Dispose();


                            ho_Contour1.Dispose();
                            HOperatorSet.GenContourPolygonXld(out ho_Contour1, hv_rowtran1.TupleConcat(hv_rowtran2),
                                hv_coltran1.TupleConcat(hv_coltran2));
                            //HOperatorSet.GenContourPolygonXld(out ho_Contour1, hv_row1.TupleConcat(hv_row2),
                            //   hv_col1.TupleConcat(hv_col2));

                            HOperatorSet.DispObj(ho_Contour1, hWindowControlC4.HalconWindow);

                            //求竖直轴与XLD的交线
                            hv_InterRow.Dispose(); hv_InterColumn.Dispose(); hv_IsOverlapping.Dispose();
                            HOperatorSet.IntersectionContoursXld(ho_ContourAngle, ho_Contour1, "all",
                                out hv_InterRow, out hv_InterColumn, out hv_IsOverlapping);
                            //求点到直线的距离

                            //修改部分@3
                            hv_Distance0.Dispose();
                            //HOperatorSet.DistancePl(hv_InterRow, hv_InterColumn, hv_FitRowBegin, hv_FitColBegin,
                            //    hv_FitRowEnd, hv_FitColEnd, out hv_Distance);
                            hv_Distance0 = hv_InterRow - (hv_CroppedRow.TupleSelect(
      (new HTuple(((new HTuple(hv_CroppedRow.TupleLength())) / 2) - 1)).TupleInt()
      ));
                        }
                        {
                            HTuple ExpTmpOutVar_0;
                            HOperatorSet.TupleAbs(hv_Distance0, out ExpTmpOutVar_0);
                            hv_Distance0.Dispose();
                            hv_Distance0 = ExpTmpOutVar_0;



                            //显示标注
                            hv_RowProj.Dispose(); hv_ColProj.Dispose();
                            //修改部分@4
                            //HOperatorSet.ProjectionPl(hv_InterRow, hv_InterColumn, hv_FitRowBegin, hv_FitColBegin,
                            //    hv_FitRowEnd, hv_FitColEnd, out hv_RowProj, out hv_ColProj);
                            HOperatorSet.ProjectionPl(hv_InterRow, hv_InterColumn, hv_CroppedRow.TupleSelect(
 (new HTuple(((new HTuple(hv_CroppedRow.TupleLength())) / 2) - 1)).TupleInt()
 ), hv_CroppedCol.TupleSelect((new HTuple(((new HTuple(hv_CroppedRow.TupleLength()
 )) / 2) - 1)).TupleInt()), hv_CroppedRow.TupleSelect((new HTuple(((new HTuple(hv_CroppedRow.TupleLength()
 )) / 2) - 1)).TupleInt()), 0, out hv_RowProj, out hv_ColProj);

                            ho_Contour2.Dispose();
                            HOperatorSet.GenContourPolygonXld(out ho_Contour2, hv_InterRow.TupleConcat(
                                hv_RowProj), hv_InterColumn.TupleConcat(hv_ColProj));

                            hv_OffsetDistance.Dispose();
                            hv_OffsetDistance = 5;
                            ho_ParallelContours.Dispose();
                            HOperatorSet.GenParallelContourXld(ho_Contour2, out ho_ParallelContours, "regression_normal",
                                hv_OffsetDistance);
                            hv_Row0.Dispose(); hv_Col0.Dispose();
                            HOperatorSet.GetContourXld(ho_ParallelContours, out hv_Row0, out hv_Col0);
                            hv_Length.Dispose();
                            HOperatorSet.TupleLength(hv_Row0, out hv_Length);


                            hv_RowProj1.Dispose(); hv_ColProj1.Dispose();
                            HOperatorSet.ProjectionPl(hv_RowProj, hv_ColProj, hv_Row0.TupleSelect(0), hv_Col0.TupleSelect(
                                0), hv_Row0.TupleSelect(1), hv_Col0.TupleSelect(1), out hv_RowProj1, out hv_ColProj1);


                            hv_RowProj2.Dispose(); hv_ColProj2.Dispose();
                            HOperatorSet.ProjectionPl(hv_InterRow, hv_InterColumn, hv_Row0.TupleSelect(0),
                                hv_Col0.TupleSelect(0), hv_Row0.TupleSelect(1), hv_Col0.TupleSelect(1), out hv_RowProj2,
                                out hv_ColProj2);

                            HOperatorSet.SetColor(hWindowControlC4.HalconWindow, "green");

                            ho_Contourl3.Dispose();
                            //修改部分@5
                            //HOperatorSet.GenContourPolygonXld(out ho_Contour3, hv_RowProj1.TupleConcat(
                            //    hv_FitRowBegin), hv_ColProj1.TupleConcat(hv_FitColBegin));
                            HOperatorSet.GenContourPolygonXld(out ho_Contourl3, hv_RowProj1.TupleConcat(
       hv_CroppedRow.TupleSelect((new HTuple(((new HTuple(hv_CroppedRow.TupleLength()
       )) / 2) - 1)).TupleInt())), hv_ColProj1.TupleConcat(hv_CroppedCol.TupleSelect(
       (new HTuple(((new HTuple(hv_CroppedRow.TupleLength())) / 2) - 1)).TupleInt()
       )));

                            ho_Contourl4.Dispose();
                            HOperatorSet.GenContourPolygonXld(out ho_Contourl4, hv_RowProj2.TupleConcat(
                                hv_InterRow), hv_ColProj2.TupleConcat(hv_InterColumn));


                            HOperatorSet.DispObj(ho_Contourl3, hWindowControlC4.HalconWindow);

                            HOperatorSet.DispObj(ho_Contourl4, hWindowControlC4.HalconWindow);

                            m_3dfun.dev_Callout_Cad(hv_Row0.TupleSelect(0), hv_Col0.TupleSelect(0), hv_Row0.TupleSelect(
          hv_Length - 1), hv_Col0.TupleSelect(hv_Length - 1), (((hv_Distance0 * 150) * 0.001)), hWindowControlC4.HalconWindow);
                            //                        m_3dfun.dev_Callout_Cad(hv_Row0.TupleSelect(0), hv_Col0.TupleSelect(0), hv_Row0.TupleSelect(
                            //                                hv_Length - 1), hv_Col0.TupleSelect(hv_Length - 1), (((hv_Distance0 * 150) * 0.001)).TupleString(
                            //".3"), hWindowControlC4.HalconWindow);

                            //计算比较测量结果并进入数据队列
                            HTuple hv_dis = hv_Distance0 * UM_PIX * 0.001;
                            double[] dis = hv_dis.ToDArr();
                            string t_str = dis[0].ToString("f2");
                            double deepval1, deepval2;
                            deepval1 = double.Parse(t_str);
                            deepval2 = double.Parse(t_str);

                            double[] m_measureval = { deepval1, deepval2 };
                            ConQueue_PassData.ElementAt(4).Item1.laserdeep_val = m_measureval;

                            string str = ConQueue_PassData.ElementAt(4).Item1.wheeltype;


                            Caliper m_caliper = new Caliper();
                            double resultval; string result;
                            m_caliper.LaserDeep_CompareDesignVal(constr, str, ConQueue_PassData.ElementAt(4).Item1.laserdeep_val, out resultval, out result);
                            ConQueue_PassData.ElementAt(4).Item1.lasercompareval = resultval;
                            ConQueue_PassData.ElementAt(4).Item1.deep_result = result;


                        }


                    }
                }

                //ho_IntersectionX.Dispose();
                //ho_region.Dispose(); ho_image.Dispose(); ho_imagecleard.Dispose();  ho_rec1.Dispose();

                //ho_CroppedContours.Dispose(); ho_Contour.Dispose(); ho_Contour1.Dispose(); ho_Contour2.Dispose();
                //ho_ParallelContours.Dispose();

                //hv_ObjectModel3D1.Dispose(); hv_Width.Dispose();
                //hv_Height.Dispose(); hv_WindowHandle.Dispose();
                //hv_BorderFact.Dispose(); hv_IsTelecentric.Dispose();
                //hv_Pose.Dispose(); hv_CamParam.Dispose();
                //hv_Labels.Dispose(); hv_VisParamName.Dispose();
                //hv_row1.Dispose(); hv_col1.Dispose();
                //hv_row2.Dispose(); hv_col2.Dispose();
                //hv_Row0.Dispose(); hv_Col0.Dispose();
                
                //hv_rowtran1.Dispose(); hv_coltran1.Dispose();
                //hv_rowtran2.Dispose(); hv_coltran2.Dispose();
                
                //hv_rowt.Dispose(); hv_coll.Dispose();
                //hv_rowb.Dispose(); hv_colr.Dispose();

                //hv_rowtrant.Dispose(); hv_coltranl.Dispose();
                //hv_rowtranb.Dispose(); hv_coltranr.Dispose();

                //hv_ModelID3.Dispose(); hv_CenterRow.Dispose();
                //hv_CenterColumn.Dispose(); hv_CenterAngle.Dispose();
                //hv_CenterScore.Dispose();

                //hv_homMat2DIdentity.Dispose();
                //hv_homMat2DRotate.Dispose();
                //hv_HomMat2DTranslate.Dispose();
                
                //hv_FitRowBegin.Dispose(); hv_FitColBegin.Dispose();
                //hv_FitRowEnd.Dispose(); hv_FitColEnd.Dispose();
                //hv_Nr.Dispose(); hv_Nc.Dispose(); hv_Dist.Dispose();
                //hv_InterRow.Dispose(); hv_InterColumn.Dispose(); hv_IsOverlapping.Dispose();
                //hv_Distance0.Dispose(); hv_RowProj.Dispose();
                //hv_ColProj.Dispose(); hv_OffsetDistance.Dispose();
                //hv_Length.Dispose(); hv_RowProj1.Dispose();
                //hv_ColProj1.Dispose(); hv_RowProj2.Dispose();
                //hv_ColProj2.Dispose();
            }
            catch (Exception ex)
            {
                logger.Info(ex.ToString());

                //if (m_message.Count > MESSAGENUM)
                //    m_message.Dequeue();
                //string str1 = "";
                //foreach (string st in m_message)
                //{
                //    str1 = str1 + st + "\n";

                //}
                //textBox_message.Text = str1;

                string ngpath = "D:\\C4NG";
                if (!Directory.Exists(ngpath))     // 返回bool类型，存在返回true，不存在返回false
                {
                    Directory.CreateDirectory(ngpath);      //不存在则创建路径

                }
                else
                {
                    m_HKVision_3D1.SavePly(ho_ObjectModel3D, ngpath + "\\" + wheeltype + DateTime.Now.ToString("yyyy-MM-dd") + "_" + c4_ngcount.ToString());
                    int num = ho_imageresult.CountObj();

                    if (num != 0)
                        HOperatorSet.WriteImage(ho_imageresult, "bmp", 0, ngpath + "\\" + wheeltype + DateTime.Now.ToString("yyyy-MM-dd") + "_" + c4_ngcount.ToString());

                    if (DateTime.Now.Hour == 24)
                        c4_ngcount = 1;
                    else
                        c4_ngcount++;

                }




                logger.Info("检测失败");

                //if (m_message.Count > MESSAGENUM)
                //    m_message.Dequeue();
                //str1 = "";
                //foreach (string st in m_message)
                //{
                //    str1 = str1 + st + "\n";

                //}
                //textBox_message.Text = str1;

            }
            finally
            {
                int connect = m_mainpccomplc4.Connect(m_ip, m_Rack, m_Slot);
                if (connect == 0)
                {
                    m_mainpccomplc4.SetBit(m_writedb, 24, 4, true);
                    // m_mainpccomplc2.DisConnect();
                }
                else
                {

                    logger.Info(res.GetString("PLC_discommun"));

                    //if (m_message.Count > MESSAGENUM)
                    //    m_message.Dequeue();
                    //string str1 = "";
                    //foreach (string st in m_message)
                    //{
                    //    str1 = str1 + st + "\n";

                    //}
                    //textBox_message.Text = str1;


                }


                TotalCountC4++;
                this.label_CountC4.Caption = res.GetString("num") + TotalCountC4.ToString();


                if (m_btnState && (Directory.Exists(C4saveimagepath)))
                {
                    string tt = C4saveimagepath + "\\" + DateTime.Now.ToString("yyyy-MM-dd") + "_";

                    m_HKVision_3D1.SavePly(ho_ObjectModel3D, tt + c4_count.ToString());
                    int num = ho_imageresult.CountObj();
                    if (num != 0)
                        HOperatorSet.WriteImage(ho_imageresult, "bmp", 0, tt + c4_count.ToString());

                    if (DateTime.Now.Hour == 24)
                        c4_count = 1;
                    else
                        c4_count++;


                }

                ho_IntersectionX.Dispose();
                ho_region.Dispose(); ho_image.Dispose(); ho_imagecleard.Dispose(); ho_imageresult.Dispose(); ho_rec1.Dispose();

                ho_CroppedContours.Dispose(); ho_Contour.Dispose(); ho_Contour1.Dispose(); ho_Contour2.Dispose();
                ho_ParallelContours.Dispose();

                hv_ObjectModel3D1.Dispose(); hv_Width.Dispose();
                hv_Height.Dispose(); hv_WindowHandle.Dispose();
                hv_BorderFact.Dispose(); hv_IsTelecentric.Dispose();
                hv_Pose.Dispose(); hv_CamParam.Dispose();
                hv_Labels.Dispose(); hv_VisParamName.Dispose();
                hv_row1.Dispose(); hv_col1.Dispose();
                hv_row2.Dispose(); hv_col2.Dispose();
                hv_Row0.Dispose(); hv_Col0.Dispose();
                hv_rowtran1.Dispose(); hv_coltran1.Dispose();
                hv_rowtran2.Dispose(); hv_coltran2.Dispose();






                hv_rowt.Dispose(); hv_coll.Dispose();
                hv_rowb.Dispose(); hv_colr.Dispose();

                hv_rowtrant.Dispose(); hv_coltranl.Dispose();
                hv_rowtranb.Dispose(); hv_coltranr.Dispose();

                hv_ModelID3.Dispose(); hv_CenterRow.Dispose();
                hv_CenterColumn.Dispose(); hv_CenterAngle.Dispose();
                hv_CenterScore.Dispose();

                hv_homMat2DIdentity.Dispose();
                hv_homMat2DRotate.Dispose();
                hv_HomMat2DTranslate.Dispose();


                hv_FitRowBegin.Dispose(); hv_FitColBegin.Dispose();
                hv_FitRowEnd.Dispose(); hv_FitColEnd.Dispose();
                hv_Nr.Dispose(); hv_Nc.Dispose(); hv_Dist.Dispose();
                hv_InterRow.Dispose(); hv_InterColumn.Dispose(); hv_IsOverlapping.Dispose();
                hv_Distance0.Dispose(); hv_RowProj.Dispose();
                hv_ColProj.Dispose(); hv_OffsetDistance.Dispose();
                hv_Length.Dispose(); hv_RowProj1.Dispose();
                hv_ColProj1.Dispose(); hv_RowProj2.Dispose();
                hv_ColProj2.Dispose();

                ho_ObjectModel3D.Dispose();
            }





            //  HOperatorSet.DispObjectModel3d(hWindowControl1.HalconWindow, hv_ObjectModel3DAffineTrans, new HTuple(),
            //new HTuple(), ((new HTuple("lut")).TupleConcat("color_attrib")).TupleConcat(
            //"disp_pose"), ((new HTuple("color1")).TupleConcat("coord_z")).TupleConcat(
            //"true"));
            // HOperatorSet.DispObjectModel3d(hWindowControl1.HalconWindow, ObjectModel3D, new HTuple(),
            //  hv_Pose, new HTuple(), new HTuple());
        }

        private void setnumberofdecimalpoint(double indata, int numble, out double outdata)
        {
            string str_data = indata.ToString();
            int str_len = str_data.Length;

            int pointindex = str_data.IndexOf(".");
            int str_left = str_len - (pointindex + 1);
            if (str_left >= numble)
            {
                string str_sub = str_data.Substring(0, pointindex + numble + 1);
                outdata = double.Parse(str_sub);
            }
            else

            {

                outdata = indata;

            }


        }


        private int Findsmallcircle(HObject ho_Image1, string typename, double regionradius, HWindowControl hWindowControl, out HTuple hv_Angle)
        {
            HObject ho_Circle, ho_Circle1, ho_RegionDifference;
            HObject ho_Contour, ho_ImageReduced;

            HOperatorSet.GenEmptyObj(out ho_Circle);
            HOperatorSet.GenEmptyObj(out ho_Circle1);
            HOperatorSet.GenEmptyObj(out ho_RegionDifference);
            HOperatorSet.GenEmptyObj(out ho_ImageReduced);
            HOperatorSet.GenEmptyObj(out ho_Contour);
            //此处为气门孔与中心孔连线的程序

            HTuple hv_ValveholeModelID = new HTuple();
            HTuple hv_ValveholeModelRow = new HTuple();
            HTuple hv_ValveholeModelColumn = new HTuple();
            HTuple hv_ValveholeModelAngle = new HTuple();
            HTuple hv_ValveholeModelScore = new HTuple();
            HTuple hv_CenterModelID = new HTuple();
            HTuple hv_CenterRow = new HTuple();
            HTuple hv_CenterColumn = new HTuple();
            HTuple hv_CenterAngle = new HTuple();
            HTuple hv_CenterScore = new HTuple();
            HTuple hubsize = new HTuple();
            try
            {
                //找中心孔
                if (File.Exists(Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + typename + "circenter.shm"))
                {

                    HOperatorSet.ReadShapeModel(Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + typename + "circenter.shm",
                                                                out hv_CenterModelID);
                    HOperatorSet.FindShapeModel(ho_Image1, hv_CenterModelID, 0, 6.28, 0.5, 1,
                     0.5, "least_squares", 5, 0.9, out hv_CenterRow, out hv_CenterColumn, out hv_CenterAngle,
                     out hv_CenterScore);

                    //找气门孔
                    hubsize[0] = regionradius;
                    ho_Circle.Dispose();
                    HOperatorSet.GenCircle(out ho_Circle, hv_CenterRow, hv_CenterColumn, hubsize);
                    ho_Circle1.Dispose();
                    HOperatorSet.GenCircle(out ho_Circle1, hv_CenterRow, hv_CenterColumn, hubsize - 200);
                    ho_RegionDifference.Dispose();
                    HOperatorSet.Difference(ho_Circle, ho_Circle1, out ho_RegionDifference);
                    ho_ImageReduced.Dispose();
                    HOperatorSet.ReduceDomain(ho_Image1, ho_RegionDifference, out ho_ImageReduced);

                    if (File.Exists(Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + typename + "valvehole .shm"))
                    {
                        HOperatorSet.ReadShapeModel(Directory.GetCurrentDirectory() + "\\model\\Measurement\\" + typename + "valvehole .shm",
                                                out hv_ValveholeModelID);
                        HOperatorSet.FindShapeModel(ho_ImageReduced, hv_ValveholeModelID, 0, 4.14, 0.55, 1,
                         0.5, "least_squares", 8, 0.9, out hv_ValveholeModelRow, out hv_ValveholeModelColumn, out hv_ValveholeModelAngle,
                         out hv_ValveholeModelScore);

                        HOperatorSet.GenContourPolygonXld(out ho_Contour, (hv_CenterRow).TupleConcat(
                        hv_ValveholeModelRow), (hv_CenterColumn).TupleConcat(hv_ValveholeModelColumn));
                        HOperatorSet.SetColor(hWindowControlC1.HalconWindow, "green");
                        HOperatorSet.DispObj(ho_Contour, hWindowControl.HalconWindow);
                        HOperatorSet.AngleLx(hv_CenterRow, hv_CenterColumn, hv_ValveholeModelRow, hv_ValveholeModelColumn, out hv_Angle);
                        
                        return 0;
                    }
                    else
                    {
                        MessageBoxEX.Show(typename + "valvehole .shm 缺失", "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                        hv_Angle = 999.0;
                        return -1;
                    }


                }
                else
                {
                    MessageBoxEX.Show(typename + "center.shm 缺失", "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                    hv_Angle = 999.0;
                    return -1;
                }

            }
            catch (Exception ex)
            {
                hv_Angle = 999.0;
                return -1;
                throw (ex);

            }
            finally
            {
                hv_ValveholeModelID.Dispose();
                hv_ValveholeModelRow.Dispose();
                hv_ValveholeModelColumn.Dispose();
                hv_ValveholeModelAngle.Dispose();
                hv_ValveholeModelScore.Dispose();
                hv_CenterModelID.Dispose();
                hv_CenterRow.Dispose();
                hv_CenterColumn.Dispose();
                hv_CenterAngle.Dispose();
                hv_CenterScore.Dispose();
                hubsize.Dispose();

                ho_Circle.Dispose();
                ho_Circle1.Dispose();
                ho_RegionDifference.Dispose();
                ho_ImageReduced.Dispose();
                ho_Contour.Dispose();
            }


        }
        //找相似的轮型
        private void Select_SimilarWheel(ref string typename)
        {
            switch (typename)
            {
                case "05017C08":
                    if (m_selectCheckListbox.Contains("05017C08"))
                    {
                        typename = "05017C08";
                    }

                    if (m_selectCheckListbox.Contains("05017C19"))
                    {
                        typename = "05017C19";
                    }
                    break;
                case "00619C69":
                    if (m_selectCheckListbox.Contains("00619C69"))
                    {
                        typename = "00619C69";
                    }

                    if (m_selectCheckListbox.Contains("00619C68"))
                    {
                        typename = "00619C68";
                    }
                    break;

            }






        }



        //****************数据类型******************************//
        public enum wheelsize
        {
            inch14, inch15, inch16, inch17, inch18, inch19, inch20, inch21, inch22, inch23, inch24, outrang

        }





        //轮毂尺寸筛选
        private void Screeninghubsize(HObject himage, HWindowControl hwcontrol, out HTuple hubsize, out HTuple centerRow, out HTuple centerColumn)
        {
            WindowParaSet winparset = new WindowParaSet();
            // Local iconic variables 

            HObject ho_ImageScaled, ho_ImageGauss;
            HObject ho_Rectangle, ho_ImageReduced, ho_Region, ho_RegionClosing;
            HObject ho_RegionFillUp, ho_ConnectedRegions, ho_ObjectSelected;
            HObject ho_Contours, ho_SelectedContours, ho_ContCircle;

            // Local control variables 

            HTuple hv_Width = new HTuple(), hv_Height = new HTuple();
            HTuple hv_Area = new HTuple(), hv_Row = new HTuple(), hv_Column = new HTuple();
            HTuple hv_Max = new HTuple(), hv_Indices = new HTuple();
            HTuple hv_Row1 = new HTuple(), hv_Column1 = new HTuple();
            HTuple hv_Radius1 = new HTuple(), hv_StartPhi = new HTuple();
            HTuple hv_EndPhi = new HTuple(), hv_PointOrder = new HTuple();
            HTuple hv_WindowHandle = new HTuple();
            // Initialize local and output iconic variables 

            HOperatorSet.GenEmptyObj(out ho_ImageScaled);
            HOperatorSet.GenEmptyObj(out ho_ImageGauss);
            HOperatorSet.GenEmptyObj(out ho_Rectangle);
            HOperatorSet.GenEmptyObj(out ho_ImageReduced);
            HOperatorSet.GenEmptyObj(out ho_Region);
            HOperatorSet.GenEmptyObj(out ho_RegionClosing);
            HOperatorSet.GenEmptyObj(out ho_RegionFillUp);
            HOperatorSet.GenEmptyObj(out ho_ConnectedRegions);
            HOperatorSet.GenEmptyObj(out ho_ObjectSelected);
            HOperatorSet.GenEmptyObj(out ho_Contours);
            HOperatorSet.GenEmptyObj(out ho_SelectedContours);
            HOperatorSet.GenEmptyObj(out ho_ContCircle);
            try
            {

                ho_ImageScaled.Dispose();
                scale_image_range(himage, out ho_ImageScaled, 30, 255);

                ho_ImageGauss.Dispose();
                HOperatorSet.GaussFilter(ho_ImageScaled, out ho_ImageGauss, 11);


                hv_Width.Dispose(); hv_Height.Dispose();
                HOperatorSet.GetImageSize(himage, out hv_Width, out hv_Height);

                using (HDevDisposeHelper dh = new HDevDisposeHelper())
                {
                    ho_Rectangle.Dispose();
                    HOperatorSet.GenRectangle1(out ho_Rectangle, 300, 400, hv_Height - 300, hv_Width - 400);
                }
                ho_ImageReduced.Dispose();
                HOperatorSet.ReduceDomain(ho_ImageGauss, ho_Rectangle, out ho_ImageReduced);


                ho_Region.Dispose();
                HOperatorSet.Threshold(ho_ImageReduced, out ho_Region, 60, 255);

                ho_RegionClosing.Dispose();
                HOperatorSet.ClosingCircle(ho_Region, out ho_RegionClosing, 30);
                ho_RegionFillUp.Dispose();
                HOperatorSet.FillUp(ho_RegionClosing, out ho_RegionFillUp);
                ho_ConnectedRegions.Dispose();
                HOperatorSet.Connection(ho_RegionFillUp, out ho_ConnectedRegions);
                hv_Area.Dispose(); hv_Row.Dispose(); hv_Column.Dispose();
                HOperatorSet.AreaCenter(ho_ConnectedRegions, out hv_Area, out hv_Row, out hv_Column);
                hv_Max.Dispose();
                HOperatorSet.TupleMax(hv_Area, out hv_Max);
                hv_Indices.Dispose();
                HOperatorSet.TupleFind(hv_Area, hv_Max, out hv_Indices);
                using (HDevDisposeHelper dh = new HDevDisposeHelper())
                {
                    ho_ObjectSelected.Dispose();
                    HOperatorSet.SelectObj(ho_ConnectedRegions, out ho_ObjectSelected, hv_Indices + 1);
                }


                ho_Contours.Dispose();
                HOperatorSet.GenContourRegionXld(ho_ObjectSelected, out ho_Contours, "border");


                ho_SelectedContours.Dispose();
                HOperatorSet.SelectContoursXld(ho_Contours, out ho_SelectedContours, "contour_length",
                    3000, 200000, -0.5, 0.5);

                //get_contour_global_attrib_xld (SelectedContours, 'cont_approx', Attrib)

                hv_Row1.Dispose(); hv_Column1.Dispose(); hv_Radius1.Dispose(); hv_StartPhi.Dispose(); hv_EndPhi.Dispose(); hv_PointOrder.Dispose();
                HOperatorSet.FitCircleContourXld(ho_SelectedContours, "algebraic", -1, 20, 0,
                    3, 2, out hv_Row1, out hv_Column1, out hv_Radius1, out hv_StartPhi, out hv_EndPhi,
                    out hv_PointOrder);

                HOperatorSet.SetLineWidth(hwcontrol.HalconWindow, 3);


                HOperatorSet.SetColor(hwcontrol.HalconWindow, "green");

                ho_ContCircle.Dispose();
                HOperatorSet.GenCircleContourXld(out ho_ContCircle, hv_Row1, hv_Column1, hv_Radius1,
                    0, 6.28318, "positive", 1);


                hubsize = hv_Radius1;
                centerRow = hv_Row1;
                centerColumn = hv_Column1;
            }
            catch (HalconException ex)
            {
                ho_ImageScaled.Dispose();
                ho_ImageGauss.Dispose();
                ho_Rectangle.Dispose();
                ho_ImageReduced.Dispose();
                ho_Region.Dispose();
                ho_RegionClosing.Dispose();
                ho_RegionFillUp.Dispose();
                ho_ConnectedRegions.Dispose();
                ho_ObjectSelected.Dispose();
                ho_Contours.Dispose();
                ho_SelectedContours.Dispose();
                ho_ContCircle.Dispose();

                hv_Width.Dispose();
                hv_Height.Dispose();
                hv_Area.Dispose();
                hv_Row.Dispose();
                hv_Column.Dispose();
                hv_Max.Dispose();
                hv_Indices.Dispose();
                hv_Row1.Dispose();
                hv_Column1.Dispose();
                hv_Radius1.Dispose();
                hv_StartPhi.Dispose();
                hv_EndPhi.Dispose();
                hv_PointOrder.Dispose();

                throw (ex);
                //hubsize = 0;
                //centerRow = 0;
                //centerColumn = 0;
                //return;

            }
            //finally
            //   {



            //       ho_ImageScaled.Dispose();
            //       ho_ImageGauss.Dispose();
            //       ho_Rectangle.Dispose();
            //       ho_ImageReduced.Dispose();
            //       ho_Region.Dispose();
            //       ho_RegionClosing.Dispose();
            //       ho_RegionFillUp.Dispose();
            //       ho_ConnectedRegions.Dispose();
            //       ho_ObjectSelected.Dispose();
            //       ho_Contours.Dispose();
            //       ho_SelectedContours.Dispose();
            //       ho_ContCircle.Dispose();

            //       hv_Width.Dispose();
            //       hv_Height.Dispose();
            //       hv_Area.Dispose();
            //       hv_Row.Dispose();
            //       hv_Column.Dispose();
            //       hv_Max.Dispose();
            //       hv_Indices.Dispose();
            //       hv_Row1.Dispose();
            //       hv_Column1.Dispose();
            //       hv_Radius1.Dispose();
            //       hv_StartPhi.Dispose();
            //       hv_EndPhi.Dispose();
            //       hv_PointOrder.Dispose();

            //   }






        }
        // Procedures 
        // External procedures 
        // Chapter: Filters / Arithmetic
        // Short Description: Scale the gray values of an image from the interval [Min,Max] to [0,255] 
        public void scale_image_range(HObject ho_Image, out HObject ho_ImageScaled, HTuple hv_Min,
            HTuple hv_Max)
        {




            // Stack for temporary objects 
            HObject[] OTemp = new HObject[20];

            // Local iconic variables 

            HObject ho_SelectedChannel = null, ho_LowerRegion = null;
            HObject ho_UpperRegion = null;

            // Local copy input parameter variables 
            HObject ho_Image_COPY_INP_TMP;
            ho_Image_COPY_INP_TMP = ho_Image.CopyObj(1, -1);



            // Local control variables 

            HTuple hv_LowerLimit = new HTuple(), hv_UpperLimit = new HTuple();
            HTuple hv_Mult = null, hv_Add = null, hv_Channels = null;
            HTuple hv_Index = null, hv_MinGray = new HTuple(), hv_MaxGray = new HTuple();
            HTuple hv_Range = new HTuple();
            HTuple hv_Max_COPY_INP_TMP = hv_Max.Clone();
            HTuple hv_Min_COPY_INP_TMP = hv_Min.Clone();

            // Initialize local and output iconic variables 
            HOperatorSet.GenEmptyObj(out ho_ImageScaled);
            HOperatorSet.GenEmptyObj(out ho_SelectedChannel);
            HOperatorSet.GenEmptyObj(out ho_LowerRegion);
            HOperatorSet.GenEmptyObj(out ho_UpperRegion);
            try
            {
                //Convenience procedure to scale the gray values of the
                //input image Image from the interval [Min,Max]
                //to the interval [0,255] (default).
                //Gray values < 0 or > 255 (after scaling) are clipped.
                //
                //If the image shall be scaled to an interval different from [0,255],
                //this can be achieved by passing tuples with 2 values [From, To]
                //as Min and Max.
                //Example:
                //scale_image_range(Image:ImageScaled:[100,50],[200,250])
                //maps the gray values of Image from the interval [100,200] to [50,250].
                //All other gray values will be clipped.
                //
                //input parameters:
                //Image: the input image
                //Min: the minimum gray value which will be mapped to 0
                //     If a tuple with two values is given, the first value will
                //     be mapped to the second value.
                //Max: The maximum gray value which will be mapped to 255
                //     If a tuple with two values is given, the first value will
                //     be mapped to the second value.
                //
                //output parameter:
                //ImageScale: the resulting scaled image
                //
                if ((int)(new HTuple((new HTuple(hv_Min_COPY_INP_TMP.TupleLength())).TupleEqual(
                    2))) != 0)
                {
                    hv_LowerLimit = hv_Min_COPY_INP_TMP[1];
                    hv_Min_COPY_INP_TMP = hv_Min_COPY_INP_TMP[0];
                }
                else
                {
                    hv_LowerLimit = 0.0;
                }
                if ((int)(new HTuple((new HTuple(hv_Max_COPY_INP_TMP.TupleLength())).TupleEqual(
                    2))) != 0)
                {
                    hv_UpperLimit = hv_Max_COPY_INP_TMP[1];
                    hv_Max_COPY_INP_TMP = hv_Max_COPY_INP_TMP[0];
                }
                else
                {
                    hv_UpperLimit = 255.0;
                }
                //
                //Calculate scaling parameters
                hv_Mult = (((hv_UpperLimit - hv_LowerLimit)).TupleReal()) / (hv_Max_COPY_INP_TMP - hv_Min_COPY_INP_TMP);
                hv_Add = ((-hv_Mult) * hv_Min_COPY_INP_TMP) + hv_LowerLimit;
                //
                //Scale image
                {
                    HObject ExpTmpOutVar_0;
                    HOperatorSet.ScaleImage(ho_Image_COPY_INP_TMP, out ExpTmpOutVar_0, hv_Mult,
                        hv_Add);
                    ho_Image_COPY_INP_TMP.Dispose();
                    ho_Image_COPY_INP_TMP = ExpTmpOutVar_0;
                }
                //
                //Clip gray values if necessary
                //This must be done for each channel separately
                HOperatorSet.CountChannels(ho_Image_COPY_INP_TMP, out hv_Channels);
                HTuple end_val48 = hv_Channels;
                HTuple step_val48 = 1;
                for (hv_Index = 1; hv_Index.Continue(end_val48, step_val48); hv_Index = hv_Index.TupleAdd(step_val48))
                {
                    ho_SelectedChannel.Dispose();
                    HOperatorSet.AccessChannel(ho_Image_COPY_INP_TMP, out ho_SelectedChannel,
                        hv_Index);
                    HOperatorSet.MinMaxGray(ho_SelectedChannel, ho_SelectedChannel, 0, out hv_MinGray,
                        out hv_MaxGray, out hv_Range);
                    ho_LowerRegion.Dispose();
                    HOperatorSet.Threshold(ho_SelectedChannel, out ho_LowerRegion, ((hv_MinGray.TupleConcat(
                        hv_LowerLimit))).TupleMin(), hv_LowerLimit);
                    ho_UpperRegion.Dispose();
                    HOperatorSet.Threshold(ho_SelectedChannel, out ho_UpperRegion, hv_UpperLimit,
                        ((hv_UpperLimit.TupleConcat(hv_MaxGray))).TupleMax());
                    {
                        HObject ExpTmpOutVar_0;
                        HOperatorSet.PaintRegion(ho_LowerRegion, ho_SelectedChannel, out ExpTmpOutVar_0,
                            hv_LowerLimit, "fill");
                        ho_SelectedChannel.Dispose();
                        ho_SelectedChannel = ExpTmpOutVar_0;
                    }
                    {
                        HObject ExpTmpOutVar_0;
                        HOperatorSet.PaintRegion(ho_UpperRegion, ho_SelectedChannel, out ExpTmpOutVar_0,
                            hv_UpperLimit, "fill");
                        ho_SelectedChannel.Dispose();
                        ho_SelectedChannel = ExpTmpOutVar_0;
                    }
                    if ((int)(new HTuple(hv_Index.TupleEqual(1))) != 0)
                    {
                        ho_ImageScaled.Dispose();
                        HOperatorSet.CopyObj(ho_SelectedChannel, out ho_ImageScaled, 1, 1);
                    }
                    else
                    {
                        {
                            HObject ExpTmpOutVar_0;
                            HOperatorSet.AppendChannel(ho_ImageScaled, ho_SelectedChannel, out ExpTmpOutVar_0
                                );
                            ho_ImageScaled.Dispose();
                            ho_ImageScaled = ExpTmpOutVar_0;
                        }
                    }
                }
                ho_Image_COPY_INP_TMP.Dispose();
                ho_SelectedChannel.Dispose();
                ho_LowerRegion.Dispose();
                ho_UpperRegion.Dispose();

                return;
            }
            catch (HalconException HDevExpDefaultException)
            {
                ho_Image_COPY_INP_TMP.Dispose();
                ho_SelectedChannel.Dispose();
                ho_LowerRegion.Dispose();
                ho_UpperRegion.Dispose();

                throw HDevExpDefaultException;
            }
        }




        private void pretreatment(HWindowControl HWControl, HObject Himage, out HObject ImageReduced)
        {

            HObject ho_Rectangle;

            // Local control variables 

            HTuple hv_Width = null, hv_Height = null;
            // Initialize local and output iconic variables 

            HOperatorSet.GenEmptyObj(out ho_Rectangle);


            HOperatorSet.GetImageSize(Himage, out hv_Width, out hv_Height);

            ho_Rectangle.Dispose();
            HOperatorSet.GenRectangle1(out ho_Rectangle, 100, 700, hv_Height / 2 + 200, hv_Width - 600);//相对稳定参数
                                                                                                        //  HOperatorSet.GenRectangle1(out ho_Rectangle, 100, 800, hv_Height / 2 + 100, hv_Width - 600);//相对稳定参数
                                                                                                        // HOperatorSet.GenRectangle1(out ho_Rectangle, 100, 1300, hv_Height/2+ 100, hv_Width - 700);
            HOperatorSet.ReduceDomain(Himage, ho_Rectangle, out ImageReduced);

            HOperatorSet.SetDraw(HWControl.HalconWindow, "margin");



            ho_Rectangle.Dispose();


        }
        #region 模板匹配
        /**************模板匹配***************************/

        private void ParallePartition(HObject imagereduce, wheelsize hubsize)
        {

            DirectoryInfo folder = new DirectoryInfo(Directory.GetCurrentDirectory() + "\\model\\" + hubsize.ToString());

            FileInfo[] files = folder.GetFiles("*.shm");
            List<HTuple> modolgroup = new List<HTuple>();
            for (int i = 0; i < files.Length; i++)
            {
                HTuple hv_ModelID = new HTuple();



                HOperatorSet.ReadShapeModel(files[i].FullName,
                                           out hv_ModelID);
                modolgroup.Add(hv_ModelID);


            }
            Parallel.For(0, files.Length, (i, statu) =>
            {
                //   System.Threading.CancellationToken ct = new CancellationToken();
                _semaphore.Wait();

                SearchPat.ResultData temp = new SearchPat.ResultData();
                HTuple hv_Row = new HTuple(), hv_Column = new HTuple(), hv_Angle = new HTuple(), hv_Score1 = new HTuple();

                HOperatorSet.FindShapeModel(imagereduce, modolgroup[i], 0, 2.5, 0.65, 1,
               0.2, "least_squares", 9, 0.9, out hv_Row, out hv_Column, out hv_Angle,
               out hv_Score1);//相对稳定参数
                HOperatorSet.ClearShapeModel(modolgroup[i]);

                //  HOperatorSet.DispObj(imagereduce, hWindowControl1.HalconID);

                if (hv_Score1.Length > 0)
                    temp.Score = hv_Score1;
                else
                    temp.Score = 0;
                temp.str = files[i].Name.Substring(0, (files[i].Name.Length - 4));
                if (hv_Score1 > 0.75)
                    statu.Stop();

                //temp.Score =(double) hv_Score1;
                //temp.str = files[i].Name.Substring(0, (files[i].Name.Length - 4));
                _keyQueue.Enqueue(temp);
                _semaphore.Release();




            });




        }
        #endregion
        #region 预处理后找轮毂型号

        private void SearchPattem(HObject ho_Image, HWindowControl hwcontrol, out string typename, out string score, out string usetime)
        {

            HObject ho_ImageReduced;
            HOperatorSet.GenEmptyObj(out ho_ImageReduced);

            //try
            //{
            // pretreatment(hwcontrol, ho_Image, out ho_ImageReduced);

            HTuple t1 = new HTuple(), t2 = new HTuple(), time = new HTuple();
            HTuple ho_width = new HTuple(), ho_height = new HTuple();//新方法
                                                                     //  resultdatd.Clear();


            HOperatorSet.CountSeconds(out t1);


            _keyQueue = new ConcurrentQueue<SearchPat.ResultData>();

            // var tAsynv = Task.Factory.StartNew(() => ParallePartition(ho_ImageReduced, hubsize));
            //   tAsynv.Wait();
            ParallePartition(ho_Image);
            Task.WaitAll();
            HOperatorSet.CountSeconds(out t2);
            ho_ImageReduced.Dispose();



            if (!(_keyQueue.Count == 0))
            {
                List<double> groupscore = new List<double>();
                for (int i = 0; i < _keyQueue.Count; i++)
                {
                    groupscore.Add(_keyQueue.ElementAt(i).Score);
                }

                double max = groupscore.Max();
                int index = groupscore.FindIndex(item => item.Equals(max));

                typename = _keyQueue.ElementAt(index).str;
                score = max.ToString();
                time = (t2 - t1);
                usetime = time.ToString().Substring(0, 3);

            }
            else
            {

                typename = "";
                score = "";
                usetime = "";
                return;
            }

            //}
            //catch (Exception ex)
            //{

            //    textBox_message.Text = ex.Message;
            //    typename = "";
            //    return;


            //}




        }
        #endregion
        #region 模板匹配
        /**************模板匹配***************************/

        private void ParallePartition(HObject imagereduce)
        {

            DirectoryInfo folder = new DirectoryInfo(Directory.GetCurrentDirectory() + "\\model\\");

            FileInfo[] files = folder.GetFiles("*.shm");
            List<HTuple> modolgroup = new List<HTuple>();
            for (int i = 0; i < files.Length; i++)
            {
                HTuple hv_ModelID = new HTuple();



                HOperatorSet.ReadShapeModel(files[i].FullName,
                                           out hv_ModelID);
                modolgroup.Add(hv_ModelID);


            }
            Parallel.For(0, files.Length, (i, statu) =>
            {
                //   System.Threading.CancellationToken ct = new CancellationToken();
                _semaphore.Wait();

                SearchPat.ResultData temp = new SearchPat.ResultData();
                HTuple hv_Row = new HTuple(), hv_Column = new HTuple(), hv_Angle = new HTuple(), hv_Score1 = new HTuple();

                HOperatorSet.FindShapeModel(imagereduce, modolgroup[i], 0, 6.28, 0.65, 1,
               0.2, "least_squares", 5, 0.9, out hv_Row, out hv_Column, out hv_Angle,
               out hv_Score1);//相对稳定参数
                HOperatorSet.ClearShapeModel(modolgroup[i]);

                //  HOperatorSet.DispObj(imagereduce, hWindowControl1.HalconID);

                if (hv_Score1.Length > 0)
                    temp.Score = hv_Score1;
                else
                    temp.Score = 0;
                temp.str = files[i].Name.Substring(0, (files[i].Name.Length - 4));
                if (hv_Score1 > 0.95)
                    statu.Stop();

                //temp.Score =(double) hv_Score1;
                //temp.str = files[i].Name.Substring(0, (files[i].Name.Length - 4));
                _keyQueue.Enqueue(temp);
                _semaphore.Release();




            });




        }
        #endregion

        #region PLC通讯

        #endregion



        private void SavedatatoPC(string typename, double angle)
        {

            NPOI np = new NPOI();
            np.dattable = new DataTable();
            np.dattable.Columns.Add("序号", typeof(Int32));
            np.dattable.Columns.Add("轮毂型号", typeof(String));
            np.dattable.Columns.Add("角度", typeof(Double));
            np.dattable.Columns.Add("结果", typeof(String));
            np.dattable.Columns.Add("时间", typeof(DateTime));

            string fruit;
            if ((typename != ""))
                fruit = "OK";
            else
                fruit = "NG";
            np.dattable.Rows.Add(new object[] { historyindex, typename, angle, fruit, DateTime.Now });

            historyindex++;
            if (historyindex == 1000)
                booknum = 0;
            if (np.dattable.Rows.Count == SAVENUM)
            {
                np.Export(Directory.GetCurrentDirectory() + "\\log\\" + booknum.ToString() + ".xls", np.dattable);
                booknum++;
                for (int i = SAVENUM - 1; i >= 0; i--)
                {
                    np.dattable.Rows.RemoveAt(i);
                }




            }

        }



        // 相机1单张采集


        //private void SingleFrame1()
        //{
        //    mCamera1.GrabOne();

        //}






        private void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            m_closed = true;
        }



        private void Form1_FormClosed(object sender, FormClosedEventArgs e)
        {

            try
            {
                ConfigIni.WriteIniKey("通讯地址", "数据库名称", Edit_Databasename.EditValue.ToString(), Application.StartupPath + "\\config.ini");

                if (createstatus1)
                {
                    m_HKCamera1.Close_Camera();
                    //mCamera1.StopGrabbing();
                    //mCamera1.CloseCam();
                }

                if (createstatus2)
                {
                    if (m_cameralink)
                    {
                        m_HKCameraLink1.Close_Camera();
                    }
                    else
                    {
                        m_HKCamera2.Close_Camera();
                    }
                    //mCamera1.StopGrabbing();
                    //mCamera1.CloseCam();
                }
                if (createstatus3)
                {
                    m_HKCamera3.Close_Camera();
                }
                if (createstatus4)
                {
                    m_HKVision_3D1.Close_Camera();

                }


                Exportconfig_CheckListBox(CListB_SelWheelType, "selectwheeeltype");

                s7commun.Close();

            }
            catch
            {

            }

        }
        //保存CheckListBox数据

        private void Exportconfig_CheckListBox(CheckedListBox checklistbox, string configname)
        {

            string stFilePath = Application.StartupPath.Trim() + "\\config\\" + configname + ".txt";

            StreamWriter swStream;

            if (File.Exists(stFilePath))
            {


                //声明数据流文件写入方法  


                swStream = new StreamWriter(stFilePath);

            }
            else
            {

                swStream = File.CreateText(stFilePath);

            }

            for (int i = 0; i < checklistbox.Items.Count; i++)
            {
                //for (int j = 0; j < checklistbox.Items[i].SubItems.Count; j++)
                //{
                string _strTemp = checklistbox.Items[i].ToString();
                swStream.Write(_strTemp);
                swStream.Write("<-->");
                _strTemp = checklistbox.GetItemChecked(i).ToString();

                swStream.Write(_strTemp);
                swStream.Write("<-->");

                //}
                swStream.WriteLine();


            }
            swStream.Flush();
            swStream.Close();

        }
        //导入CheckListBox数据
        public void Importconfig_CheckListBox(CheckedListBox checklistbox, string namecongfig)
        {
            string stFilePath = Application.StartupPath.Trim() + "\\config\\" + namecongfig + ".txt";

            StreamReader srStream = null;

            if (File.Exists(stFilePath))
            {
                srStream = new StreamReader(stFilePath, System.Text.Encoding.UTF8);
                checklistbox.Items.Clear();
                //遍历txt文件里的所有行，并以ListView显示
                int i = 0;
                while (!srStream.EndOfStream)
                {
                    string _stLineValue = srStream.ReadLine();


                    List<string> InData = new List<string>();
                    string[] sArray = _stLineValue.Split(new string[] { "<-->", "<-->", "<-->" }, StringSplitOptions.RemoveEmptyEntries);

                    checklistbox.Items.Add(sArray[0]);
                    if (sArray[1] == "True")
                        checklistbox.SetItemChecked(i, true);
                    else
                        checklistbox.SetItemChecked(i, false);
                    i++;
                }
                srStream.Close();
            }
            else
                MessageBoxEX.Show(res.GetString("importFailed") + namecongfig + res.GetString("file"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });


        }

        // Shows exceptions in a message box.
        private void ShowException(Exception exception)
        {
            MessageBox.Show("Exception caught:\n" + exception.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }



        // 执行相机1软触发命令
        private void SoftwareExecute1()
        {
            // mCamera1.SendSoftwareExecute();
            m_HKCamera1.SoftTrigger();
            //  m_HKCamera2.SoftTrigger();
        }

        // 执行相机2软触发命令
        private void SoftwareExecute2()
        {
            logger.Info("C2 soft trigger");
            if (m_cameralink)
            {
                m_HKCameraLink1.SoftTrigger();
            }
            else
            {
                m_HKCamera2.SoftTrigger();
            }
        }
        // 执行相机3软触发命令
        private void SoftwareExecute3()
        {
            // mCamera1.SendSoftwareExecute();
            m_HKCamera3.SoftTrigger();
            //  m_HKCamera2.SoftTrigger();
        }
        //执行相机4软触发命令
        private void SoftwareExecute4()
        {
            // mCamera1.SendSoftwareExecute();
            m_HKVision_3D1.SoftTrigger();
            //  m_HKCamera2.SoftTrigger();
        }

        //相机1瀑光设置
        private void Exposure1(int value)
        {

            //mCamera1.SetExposureTime(value);
            m_HKCamera1.SetExposure(value);

        }
        //相机2瀑光设置
        private void Exposure2(int value)
        {

            //mCamera1.SetExposureTime(value);
            if (m_cameralink)
            {
                //m_HKCameraLink1.SetExposure(value);
            }
            else
            {
                m_HKCamera2.SetExposure(value);
            }

        }
        //相机2瀑光设置
        private void Exposure3(int value)
        {

            //mCamera1.SetExposureTime(value);
            m_HKCamera3.SetExposure(value);

        }


        //相机1增益设置
        private void Gain1(int value)
        {

            //mCamera1.SetGain(value);
            m_HKCamera1.SetGain(value);

        }
        //相机2增益设置
        private void Gain2(int value)
        {

            //mCamera1.SetGain(value);
            if (m_cameralink)
            {
                //m_HKCameraLink1.SetGain(value);
            }
            else
            {
                m_HKCamera2.SetGain(value);
            }

        }
        //相机2增益设置
        private void Gain3(int value)
        {

            //mCamera1.SetGain(value);
            m_HKCamera3.SetGain(value);

        }

        // 设置相机１为Freerun模式

        private void Freerun1()
        {
            // mCamera1.SetFreerun();
            m_HKCamera1.SetContinuesMode();


        }
        // 设置相机2为Freerun模式
        private void Freerun2()
        {
            // mCamera1.SetFreerun();

            if (m_cameralink)
            {
                m_HKCameraLink1.SetContinuesMode();
            }
            else
            {
                m_HKCamera2.SetContinuesMode();
            }

        }
        // 设置相机3为Freerun模式
        private void Freerun3()
        {
            // mCamera1.SetFreerun();

            m_HKCamera3.SetContinuesMode();

        }



        //设置相机1为触发模式
        private void Trigger1()
        {
            m_HKCamera1.SetTriggerMode();

        }
        //设置相机2为触发模式
        private void Trigger2()
        {
            if (m_cameralink)
            {
                m_HKCameraLink1.SetTriggerMode();
            }
            else
            {
                m_HKCamera2.SetTriggerMode();
            }
        }
        //设置相机3为触发模式
        private void Trigger3()
        {

            m_HKCamera3.SetTriggerMode();
        }



        // 设置相机１为软触发模式

        private void SetSoftTrigger1()
        {

            m_HKCamera1.SetSoftTrigger();


        }
        // 设置相机2为软触发模式
        private void SetSoftTrigger2()
        {
            if (m_cameralink)
            {
                m_HKCameraLink1.SetSoftTrigger();
            }
            else
            {
                m_HKCamera2.SetSoftTrigger();
            }
        }

        // 设置相机3为软触发模式
        private void SetSoftTrigger3()
        {
            //mCamera1.SetSoftwareTrigger();

            m_HKCamera3.SetSoftTrigger();

        }


        // 设置相机１为外触发模式

        private void SetExternTrigger1()
        {
            //mCamera1.SetExternTrigger();
            m_HKCamera1.SetExternTrigger();

        }
        // 设置相机2为外触发模式
        private void SetExternTrigger2()
        {
            if (m_cameralink)
            {
                m_HKCameraLink1.SetExternTrigger();
            }
            else
            {
                m_HKCamera2.SetExternTrigger();
            }

        }

        // 设置相机3为外触发模式
        private void SetExternTrigger3()
        {
            m_HKCamera3.SetExternTrigger();

        }



        private void Findsmallcircle(string typename, double regionradius, HWindowControl hWindowControl, out HTuple hv_Angle)
        {
            HObject ho_Circle, ho_Circle1, ho_RegionDifference;
            HObject ho_Contour, ho_ImageReduced;

            HOperatorSet.GenEmptyObj(out ho_Circle);
            HOperatorSet.GenEmptyObj(out ho_Circle1);
            HOperatorSet.GenEmptyObj(out ho_RegionDifference);
            HOperatorSet.GenEmptyObj(out ho_ImageReduced);
            HOperatorSet.GenEmptyObj(out ho_Contour);
            //此处为气门孔与中心孔连线的程序

            HTuple hv_ValveholeModelID = null, hv_ValveholeModelRow = null, hv_ValveholeModelColumn = null, hv_ValveholeModelAngle = null, hv_ValveholeModelScore = null;
            HTuple hv_CenterModelID = null, hv_CenterRow = null, hv_CenterColumn = null, hv_CenterAngle = null, hv_CenterScore = null;
            HTuple hubsize = new HTuple();

            //找中心孔
            HOperatorSet.ReadShapeModel(Directory.GetCurrentDirectory() + "\\model\\angle\\" + typename + "center.shm",
                                         out hv_CenterModelID);
            HOperatorSet.FindShapeModel(ho_Image, hv_CenterModelID, 0, 6.28, 0.5, 1,
             0.5, "least_squares", 5, 0.9, out hv_CenterRow, out hv_CenterColumn, out hv_CenterAngle,
             out hv_CenterScore);

            //找气门孔
            hubsize[0] = regionradius;
            ho_Circle.Dispose();
            HOperatorSet.GenCircle(out ho_Circle, hv_CenterRow, hv_CenterColumn, hubsize);
            ho_Circle1.Dispose();
            HOperatorSet.GenCircle(out ho_Circle1, hv_CenterRow, hv_CenterColumn, hubsize - 200);
            ho_RegionDifference.Dispose();
            HOperatorSet.Difference(ho_Circle, ho_Circle1, out ho_RegionDifference);
            ho_ImageReduced.Dispose();
            HOperatorSet.ReduceDomain(ho_Image, ho_RegionDifference, out ho_ImageReduced);

            HOperatorSet.ReadShapeModel(Directory.GetCurrentDirectory() + "\\model\\angle\\" + typename + "valvehole .shm",
                                        out hv_ValveholeModelID);
            HOperatorSet.FindShapeModel(ho_ImageReduced, hv_ValveholeModelID, 0, 4.14, 0.55, 1,
             0.5, "least_squares", 8, 0.9, out hv_ValveholeModelRow, out hv_ValveholeModelColumn, out hv_ValveholeModelAngle,
             out hv_ValveholeModelScore);

            HOperatorSet.GenContourPolygonXld(out ho_Contour, (hv_CenterRow).TupleConcat(
                hv_ValveholeModelRow), (hv_CenterColumn).TupleConcat(hv_ValveholeModelColumn));
            HOperatorSet.SetColor(hWindowControlC3.HalconWindow, "green");
            HOperatorSet.DispObj(ho_Contour, hWindowControl.HalconWindow);
            HOperatorSet.AngleLx(hv_CenterRow, hv_CenterColumn, hv_ValveholeModelRow, hv_ValveholeModelColumn, out hv_Angle);

            ho_Circle.Dispose();
            ho_Circle1.Dispose();
            ho_RegionDifference.Dispose();
            ho_ImageReduced.Dispose();
            ho_Contour.Dispose();
        }








        private void Disp_systemtiem(object sender, EventArgs e)
        {

            label_Systermtime.Caption = DateTime.Now.ToString();


        }


        private void Btm_S7_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //S7communication s7commun1 = new S7communication();

            s7commun.Show();


        }
        private void DisConnectPlc()
        {
            m_plcstatus = false;


        }

        Queue<Action> actionQueue = new Queue<Action>();
        Mutex queueMutex = new Mutex();

        private void ActionExecThread()
        {
            while (true)
            {
                Action action = null;
                queueMutex.WaitOne();
                if (actionQueue.Count > 0)
                {
                    action = actionQueue.Dequeue();
                }
                queueMutex.ReleaseMutex();

                if (action == null)
                {
                    Thread.Sleep(10);
                    continue;
                }
                action();
            }
        }
        
        private void GetPlcData2(string ip, int Rack, int Slot, int readdb, int writedb)
        {
            try
            {
                int connection = m_mainpccomplc.Connect(ip, Rack, Slot);
                if (connection == 0)
                {
                    m_plcstatus = true;
                    mPLCReonnCount = 0;
                    s7commun.tB_Status.BackColor = Color.Green;

                    var actionList = new List<Tuple<int, int, int, bool, Action>>() {
                        { new Tuple<int, int, int, bool, Action>(0, 0, 0, false, ActionGetWheelType) },         // 接收轮毂型号
                        { new Tuple<int, int, int, bool, Action>(0, 1, 0, false, ActionLasermovval) },          // 发送激光工位移动高度
                        //{ new Tuple<int, int, int, bool, Action>(0, 2, 0, false, ActionGetDeepval) },         // 接收深度值
                        { new Tuple<int, int, int, bool, Action>(0, 3, 0, false, ActionCenholmovval) },         // 发送中心孔工位移动高度
                        { new Tuple<int, int, int, bool, Action>(0, 4, 0, false, SoftwareExecute2) },           // 中心孔相机触发拍照
                        { new Tuple<int, int, int, bool, Action>(0, 5, 0, false, ActionHatholmoveval) },        // 发送帽止口顶升高度
                        { new Tuple<int, int, int, bool, Action>(0, 6, 0, false, SoftwareExecute3) },           // 帽止口相机触发拍照
                        { new Tuple<int, int, int, bool, Action>(0, 7, 0, false, ActionMarkval) },              // 是否可以取标记高度
                        { new Tuple<int, int, int, bool, Action>(1, 0, 0, false, ActionDatamove) },             // 数据移位  
                        { new Tuple<int, int, int, bool, Action>(1, 1, 0, false, SoftwareExecute1) },           // 轮型识别相机触发拍照
                        { new Tuple<int, int, int, bool, Action>(1, 2, 0, false, SoftwareExecute4) },           // 激光相机触发拍照
                        { new Tuple<int, int, int, bool, Action>(1, 4, 0, false, ActionThicknessmoveval) },     // 发送厚度检测顶升高度
                        { new Tuple<int, int, int, bool, Action>(1, 5, 0, false, ActionGetThinknessresult) },   // 接收厚度检测结果
                        //{ new Tuple<int, int, int, bool, Action>(1, 6, 0, false, ActionGetThinkswitch) },     // 发送厚度检测开关
                        { new Tuple<int, int, int, bool, Action>(278, 0, 0, false, ActionBoltholedata) },       // 发送螺栓孔数据
                    };

                    while (m_plcstatus)
                    {
                        Thread.Sleep(20);
                        try
                        {
                            if (!m_mainpccomplc.Connected)
                            {
                                if (m_mainpccomplc.Connect(ip, Rack, Slot) != 0)
                                {
                                    logger.Error("plc disconnected");
                                    break;
                                }
                            }

                            int buf = m_mainpccomplc.GetIntLittleEndian(readdb, 0);
                            for (int i = 0; i < actionList.Count; ++i)
                            {
                                var actionTuple = actionList[i];

                                int byteIndex = actionTuple.Item1;
                                int bitIndex = actionTuple.Item2;

                                bool signal = false;
                                if (byteIndex <= 1)
                                {
                                    signal = (buf & (1 << (byteIndex * 8 + bitIndex))) != 0;
                                }
                                else
                                {
                                    signal = m_mainpccomplc.Getbit(readdb, byteIndex, bitIndex);
                                }

                                if (!signal)
                                {
                                    if (actionTuple.Item4 == true || actionTuple.Item3 != 0)
                                    {
                                        var action = actionTuple.Item5;
                                        actionTuple = new Tuple<int, int, int, bool, Action>(byteIndex, bitIndex, 0, false, action);
                                        actionList[i] = actionTuple;
                                    }
                                }
                                else
                                {
                                    if (actionTuple.Item4 == false)
                                    {
                                        var action = actionTuple.Item5;
                                        if (actionTuple.Item3 < 1)
                                        {

                                            actionTuple = new Tuple<int, int, int, bool, Action>(byteIndex, bitIndex, actionTuple.Item3 + 1, false, action);
                                            actionList[i] = actionTuple;
                                        }
                                        else
                                        {
                                            actionTuple = new Tuple<int, int, int, bool, Action>(byteIndex, bitIndex, 0, true, action);
                                            actionList[i] = actionTuple;

                                            queueMutex.WaitOne();
                                            logger.Info("enqueue " + byteIndex.ToString() + "," + bitIndex.ToString() + "  " + action.Method.Name);
                                            actionQueue.Enqueue(action);
                                            queueMutex.ReleaseMutex();
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception e)
                        {
                            logger.Error(e.Message + e.StackTrace);
                        }

                    }

                    m_mainpccomplc.DisConnect();
                    logger.Info("plc连接断开");
                }
                else
                {
                    logger.Info("plc连接失败");
                }


                s7commun.tB_Status.BackColor = Color.Red;
                s7commun.TextError.Text = res.GetString("disconnected");
                s7commun.DisconnectBtn.Enabled = false;
                s7commun.ConnectBtn.Enabled = true;

                if (connection != 0 || m_plcstatus == true)
                {
                    // 未连接成功或非正常断开连接，尝试重连
                    m_plcstatus = false;

                    if (mPLCReonnCount < 3)
                    {
                        mPLCReonnCount += 1;
                        new Thread(() =>
                        {
                            logger.Info("plc reconnecting...");
                            GetPlcData2(ip, Rack, Slot, readdb, writedb);
                            Thread.Sleep(1000 + mPLCReonnCount * 1000);
                        }).Start();
                    }
                }
            }
            catch (Exception e)
            {
                logger.Error(e.Message + e.StackTrace);
            }

        }

        private int _id = 5;

        // #0 接收轮毂型号
        private void ActionGetWheelType()
        {
            try
            {
                string wheeltype = m_mainpccomplc.GetString(m_readdb, 22);//轮毂型号
                logger.Info("#0 轮型 [" + _id.ToString() + "," + wheeltype);

                PassData passdata = new PassData();
                passdata.wheeltype = wheeltype;

                if (HasWheel(wheeltype))
                {
                    passdata.lasermov_val = GetWheelParam(wheeltype, "深度检测工位伺服移动");
                    passdata.lasermovH_val = GetWheelParam(wheeltype, "深度检测工位伺服水平移动");
                    passdata.cenholmov_val = GetWheelParam(wheeltype, "中心孔工位伺服移动");
                    passdata.hatholmov_val = GetWheelParam(wheeltype, "帽止口工位伺服移动");
                    passdata.markmov_val = GetWheelParam(wheeltype, "标记工位伺服移动");
                    passdata.thinknessmov_val = GetWheelParam(wheeltype, "螺栓孔厚度工位伺服移动");

                    passdata.deep_stdval = GetWheelParam(wheeltype, "帽槽深度");
                    passdata.deeptolp_stdval = GetWheelParam(wheeltype, "帽槽深度上公差");
                    passdata.deeptoln_stdval = GetWheelParam(wheeltype, "帽槽深度下公差");

                    passdata.cenhol_stdval = GetWheelParam(wheeltype, "中心孔直径");
                    passdata.cenholtolp_stdval = GetWheelParam(wheeltype, "中心孔直径上公差");
                    passdata.cenholtoln_stdval = GetWheelParam(wheeltype, "中心孔直径下公差");

                    passdata.hole_num = (int)GetWheelParam(wheeltype, "孔数");
                    passdata.PCD_stdval = GetWheelParam(wheeltype, "PCD节圆直径");

                    logger.Info("取" + passdata.wheeltype + "的配方成功");


                    {
                        double lasermov_val = passdata.lasermov_val;
                        double lasermovH_val = passdata.lasermovH_val;
                        double regionsize1 = 0.0, regionsize2 = 0.0;

                        regionsize1 = GetWheelParam(wheeltype, "轮芯厚度");
                        regionsize2 = GetWheelParam(wheeltype, "后距空间");

                        float result1 = (float)(m_deepZ - regionsize1 - regionsize2 - 120 + lasermov_val);
                        m_mainpccomplc.SetFloat(m_writedb, 0, result1);
                        m_mainpccomplc.SetFloat(m_writedb, 830, (float)(m_deepX + lasermovH_val));
                        m_mainpccomplc.SetInt(m_writedb, 4, 666);
                        logger.Info(wheeltype + "发送深度检测工位高度 " + result1.ToString() + ", " + (m_deepX + lasermovH_val).ToString());
                    }
                }
                else
                {
                    logger.Error("未查询到 " + wheeltype + " 的配方");
                }
                
                ConQueue_PassData[4] = new Tuple<PassData, int>(passdata, _id++);//进入队列
            }
            catch (Exception e)
            {
                logger.Error(e.Message + e.StackTrace);
            }
        }

        // #1 发送激光工位移动高度
        private void ActionLasermovval()
        {
            logger.Info("#1 [" + ConQueue_PassData[4].Item2.ToString() + ", " + ConQueue_PassData[4].Item1.wheeltype);

            if (ConQueue_PassData.ElementAt(4).Item1.wheeltype == "NG")
            {
                m_mainpccomplc.SetFloat(m_writedb, 0, 0);
                m_mainpccomplc.SetFloat(m_writedb, 830, 0);
                m_mainpccomplc.SetInt(m_writedb, 4, 666);
                logger.Info("深度检测工位高度已发送");
            }
            else
            {
                double lasermov_val = ConQueue_PassData.ElementAt(4).Item1.lasermov_val;
                double lasermovH_val = ConQueue_PassData.ElementAt(4).Item1.lasermovH_val;
                string wheeltype = ConQueue_PassData.ElementAt(4).Item1.wheeltype;
                double regionsize1 = 0.0, regionsize2 = 0.0;

                if (HasWheel(wheeltype))
                {
                    regionsize1 = GetWheelParam(wheeltype, "轮芯厚度");
                    regionsize2 = GetWheelParam(wheeltype, "后距空间");

                    float result1 = (float)(m_deepZ - regionsize1 - regionsize2 - 120 + lasermov_val);
                    m_mainpccomplc.SetFloat(m_writedb, 0, result1);
                    m_mainpccomplc.SetFloat(m_writedb, 830, (float)(m_deepX + lasermovH_val));
                    m_mainpccomplc.SetInt(m_writedb, 4, 666);
                    logger.Info(wheeltype + "深度检测工位高度已发送 " + result1.ToString() + ", " + (m_deepX + lasermovH_val).ToString());
                }
                else
                {
                    logger.Error("未查询到 " + wheeltype + " 的配方");
                }
            }
        }

        // #2 接收深度值
        private void ActionGetDeepval()
        {
            logger.Info("#2 [" + ConQueue_PassData[4].Item2.ToString() + ", " + ConQueue_PassData[4].Item1.wheeltype);

            double deepval1 = m_mainpccomplc.GetFloat(m_readdb, 2);//深度值
            double deepval2 = m_mainpccomplc.GetFloat(m_readdb, 6);//深度值

            //截取小数点后面2位
            string t_str = deepval1.ToString("f2");
            deepval1 = double.Parse(t_str);
            t_str = deepval2.ToString("f2");
            deepval2 = double.Parse(t_str);

            double[] m_measureval = { deepval1, deepval2 };
            ConQueue_PassData.ElementAt(4).Item1.laserdeep_val = m_measureval;

            string str = ConQueue_PassData.ElementAt(4).Item1.wheeltype;


            Caliper m_caliper = new Caliper();
            double resultval; string result;
            m_caliper.LaserDeep_CompareDesignVal(constr, str, ConQueue_PassData.ElementAt(4).Item1.laserdeep_val, out resultval, out result);
            ConQueue_PassData.ElementAt(4).Item1.lasercompareval = resultval;
            ConQueue_PassData.ElementAt(4).Item1.deep_result = result;

            logger.Info(str + "深度检测值已接收\n\r");
        }

        // #3 发送中心孔工位移动高度
        private void ActionCenholmovval()
        {
            logger.Info("#3 [" + ConQueue_PassData[3].Item2.ToString() + ", " + ConQueue_PassData[3].Item1.wheeltype);

            double lasermov_val = ConQueue_PassData.ElementAt(3).Item1.cenholmov_val;
            string wheeltype = ConQueue_PassData.ElementAt(3).Item1.wheeltype;
            double regionsize1 = 0.0;
            double light = 0.0;

            try
            {
                if (wheeltype == "NG")
                {
                    m_mainpccomplc.SetFloat(m_writedb, 6, 0);
                    m_mainpccomplc.SetInt(m_writedb, 10, 666);
                    logger.Info("中心孔工位高度已发送");
                }
                else
                {
                    double exposureTime = 0;
                    if(HasWheel(wheeltype))
                    {
                        regionsize1 = GetWheelParam(wheeltype, "后距空间");
                        exposureTime = GetWheelParam(wheeltype, "中心孔曝光时长");
                        light = GetWheelParam(wheeltype, "中心孔环光开关");
                    }
                    else
                    {
                        logger.Error("未查询到 " + wheeltype + " 的配方");
                    }

                    float result1 = (float)(410 - regionsize1 - m_centreZ - lasermov_val);

                    m_mainpccomplc.SetFloat(m_writedb, 6, result1);
                    m_mainpccomplc.SetBit(m_writedb, 834, 0, (int)light != 0);
                    m_mainpccomplc.SetInt(m_writedb, 10, 666);
                    logger.Info(wheeltype + "中心孔工位高度已发送 " + result1.ToString());

                    if (exposureTime <= 0)
                    {
                        exposureTime = m_defExposureTimeCen;
                    }
                    logger.Info(wheeltype + " cenhole exposure: " + exposureTime);
                    if (m_cameralink)
                    {
                        m_HKCameraLink1.SetExposure((float)exposureTime);
                    }
                    else
                    {
                        m_HKCamera2.SetExposure((float)exposureTime);
                    }
                }
            }
            catch (Exception e)
            {
                logger.Error(e.Message + e.StackTrace);
                m_mainpccomplc.SetFloat(m_writedb, 6, (float)-30);
                m_mainpccomplc.SetInt(m_writedb, 10, 999);
            }
        }

        // #5 发送帽止口顶升高度
        private void ActionHatholmoveval()
        {
            logger.Info("#5 [" + ConQueue_PassData[2].Item2.ToString() + ", " + ConQueue_PassData[2].Item1.wheeltype);

            if (ConQueue_PassData.ElementAt(2).Item1.wheeltype == "NG")
            {
                m_mainpccomplc.SetFloat(m_writedb, 12, 0);
                m_mainpccomplc.SetInt(m_writedb, 16, 666);
                logger.Info("帽止口检测工位高度已发送");
            }
            else
            {
                double lasermov_val = ConQueue_PassData.ElementAt(2).Item1.hatholmov_val;
                string wheeltype = ConQueue_PassData.ElementAt(2).Item1.wheeltype;

                double exposureTime = 0;
                double regionsize1 = 0.0, regionsize2 = 0.0, regionsize3 = 0.0;
                double frontLight = 0.0;
                regionsize3 = m_hatZ;
                
                if (HasWheel(wheeltype))
                {
                    regionsize1 = GetWheelParam(wheeltype, "轮芯厚度");
                    regionsize2 = GetWheelParam(wheeltype, "后距空间");
                    exposureTime = GetWheelParam(wheeltype, "帽止口曝光时长");
                    frontLight = GetWheelParam(wheeltype, "帽止口正面光开关");
                }
                else
                {
                    logger.Error("未查询到 " + wheeltype + " 的配方");
                }

                float result1 = (float)(regionsize3 - regionsize1 - regionsize2 - m_hatFocal + lasermov_val);
                
                m_mainpccomplc.SetFloat(m_writedb, 12, (float)result1);
                m_mainpccomplc.SetBit(m_writedb, 24, 6, (int)frontLight != 0);
                m_mainpccomplc.SetInt(m_writedb, 16, 666);
                logger.Info(wheeltype + "帽止口检测工位高度已发送 " + result1.ToString());


                if (exposureTime <= 0)
                {
                    exposureTime = m_defExposureTimeHat;
                }
                logger.Info(wheeltype + " hathole exposure: " + exposureTime);
                m_HKCamera3.SetExposure((float)exposureTime);
            }
        }

        // #7 发生打钢印高度，和最终结果
        private void ActionMarkval()
        {
            logger.Info("#7 [" + ConQueue_PassData[0].Item2.ToString() + ", " + ConQueue_PassData[0].Item1.wheeltype);

            try
            {
                double thickSwitch = 0.0;
                if (ConQueue_PassData.ElementAt(0).Item1.wheeltype == "NG")
                {
                    m_mainpccomplc.SetFloat(m_writedb, 18, 0);
                    m_mainpccomplc.SetInt(m_writedb, 22, 666);
                    logger.Info("打钢印工位高度已发送");
                }
                else
                {
                    double lasermov_val = ConQueue_PassData.ElementAt(0).Item1.markmov_val;
                    string wheeltype = ConQueue_PassData.ElementAt(0).Item1.wheeltype;

                    double regionsize1 = 0.0, regionsize2 = 0.0, regionsize3 = 0.0;
                    regionsize3 = m_markZ;

                    if (HasWheel(wheeltype))
                    {
                        regionsize1 = GetWheelParam(wheeltype, "总高");
                        thickSwitch = GetWheelParam(wheeltype, "厚度检测开关");
                    }
                    else
                    {
                        logger.Error("未查询到 " + wheeltype + " 的配方");
                    }
                    float result1 = -(float)(regionsize3 - regionsize1 + 56 + lasermov_val);

                    m_mainpccomplc.SetFloat(m_writedb, 18, (float)result1);
                    logger.Info(wheeltype + "打钢印工位高度已发送 " + result1.ToString());
                }


                try
                {

                    //最终结果判断
                    string rel3 = ConQueue_PassData[0].Item1.deep_result;
                    string rel2 = ConQueue_PassData[0].Item1.cenhol_result;
                    string[] rel1 = ConQueue_PassData[0].Item1.pos_result;
                    string[] rel4 = ConQueue_PassData[0].Item1.boltdiameter_result;
                    string[] rel5 = ConQueue_PassData[0].Item1.boltthinkness_result;
                    string rel0 = ConQueue_PassData[0].Item1.hathol_result;

                    List<string> rel = new List<string>();
                    rel.Add(rel3);
                    rel.Add(rel2);
                    for (int i = 0; i < ConQueue_PassData[0].Item1.hole_num; i++)
                    {
                        rel.Add(rel1[i]);
                    }
                    for (int i = 0; i < ConQueue_PassData[0].Item1.hole_num; i++)
                    {
                        rel.Add(rel4[i]);
                    }
                    if (thickSwitch > 0)
                    {
                        for (int i = 0; i < ConQueue_PassData[0].Item1.hole_num; i++)
                        {
                            rel.Add(rel5[i]);
                        }
                    }
                    rel.Add(rel0);

                    if (ConQueue_PassData[0].Item1.wheeltype != "empty")
                    {
                        if ((rel.Contains("NG")) || (rel.Contains(null)))
                        {
                            ConQueue_PassData[0].Item1.last_result = "NG";
                        }
                        else
                        {
                            ConQueue_PassData[0].Item1.last_result = "OK";
                        }
                    }
                    else
                    {
                        ConQueue_PassData[0].Item1.last_result = "NG";
                    }


                    //发送检测最后结果
                    if (!string.IsNullOrEmpty(ConQueue_PassData[0].Item1.wheeltype))
                    {
                        m_mainpccomplc.SetString(m_writedb, 298, ConQueue_PassData[0].Item1.last_result);
                        m_mainpccomplc.SetString(m_writedb, 836, ConQueue_PassData[0].Item1.wheeltype);
                    }

                    if (m_sendNGPosition && !string.IsNullOrEmpty(ConQueue_PassData[0].Item1.wheeltype))
                    {
                        var data = ConQueue_PassData[0].Item1;
                        byte ngPos = 0;
                        if (string.IsNullOrEmpty(data.wheeltype) || data.wheeltype.Equals("NG"))
                        {
                            ngPos = 0x7F;
                        }
                        else
                        {
                            var boltdiameter_result = ConQueue_PassData[0].Item1.boltdiameter_result.ToList();
                            var pos_result = ConQueue_PassData[0].Item1.pos_result.ToList();
                            var boltthinkness_result = ConQueue_PassData[0].Item1.boltthinkness_result.ToList();
                            while (boltdiameter_result.Count > ConQueue_PassData[0].Item1.hole_num)
                            {
                                boltdiameter_result.RemoveAt(ConQueue_PassData[0].Item1.hole_num);
                                pos_result.RemoveAt(ConQueue_PassData[0].Item1.hole_num);
                                boltthinkness_result.RemoveAt(ConQueue_PassData[0].Item1.hole_num);
                            }

                            ngPos = (byte)(ngPos | (string.IsNullOrEmpty(data.deep_result) || data.deep_result.Equals("NG") ? 1 : 0) << 5);
                            ngPos = (byte)(ngPos | (string.IsNullOrEmpty(data.cenhol_result) || data.cenhol_result.Equals("NG") ? 1 : 0) << 4);
                            ngPos = (byte)(ngPos | (boltdiameter_result.Contains("NG") ? 1 : 0) << 3);
                            ngPos = (byte)(ngPos | (boltdiameter_result.Contains("") ? 1 : 0) << 3);
                            ngPos = (byte)(ngPos | (boltdiameter_result.Contains(null) ? 1 : 0) << 3);
                            ngPos = (byte)(ngPos | (pos_result.Contains("NG") ? 1 : 0) << 2);
                            ngPos = (byte)(ngPos | (pos_result.Contains("") ? 1 : 0) << 2);
                            ngPos = (byte)(ngPos | (pos_result.Contains(null) ? 1 : 0) << 2);
                            ngPos = (byte)(ngPos | (string.IsNullOrEmpty(data.hathol_result) || data.hathol_result.Equals("NG") ? 1 : 0) << 1);

                            if (thickSwitch > 0)
                            {
                                ngPos = (byte)(ngPos | (boltthinkness_result.Contains("NG") ? 1 : 0) << 0);
                                ngPos = (byte)(ngPos | (boltthinkness_result.Contains("") ? 1 : 0) << 0);
                                ngPos = (byte)(ngPos | (boltthinkness_result.Contains(null) ? 1 : 0) << 0);
                            }
                        }

                        m_mainpccomplc.SetByte(m_writedb, 1092, ngPos);
                    }
                    else
                    {
                        m_mainpccomplc.SetByte(m_writedb, 1092, 0);
                    }

                }
                catch (Exception ex)
                {
                    logger.Error(ex.Message + ex.StackTrace);
                }

                m_mainpccomplc.SetInt(m_writedb, 22, 666);
                logger.Info(ConQueue_PassData[0].Item1.wheeltype + " [" + ConQueue_PassData[0].Item2.ToString() + " 最终结果已发送");
            }
            catch (Exception ex)
            {
                m_mainpccomplc.SetInt(m_writedb, 22, 999);
                logger.Error(ex.Message + ex.StackTrace);
            }
        }

        // #8 数据移位
        private void ActionDatamove()
        {
            logger.Info("#8 [" + ConQueue_PassData[0].Item2.ToString());

            try
            {
                PassData m_passdata = new PassData();
                PassData m_nullpassdata = new PassData();

                m_passdata = ConQueue_PassData[0].Item1;
                if (m_MEGAPHASE)
                {
                    ConQueue_PassData[0] = ConQueue_PassData[1];
                    ConQueue_PassData[1] = ConQueue_PassData[2];
                }
                else
                {
                    ConQueue_PassData[0] = ConQueue_PassData[2];
                }
                logger.Info("next " + ConQueue_PassData[0].Item2.ToString());
                ConQueue_PassData[2] = ConQueue_PassData[3];
                ConQueue_PassData[3] = ConQueue_PassData[4];
                ConQueue_PassData[4] = new Tuple<PassData, int>(m_nullpassdata, -1);


                if (!string.IsNullOrEmpty(m_passdata.wheeltype) && m_passdata.wheeltype != "empty")
                {
                    if ("".Equals(m_passdata.last_result))
                    {
                        m_passdata.last_result = "NG";
                    }
                    SQlFun sqlfun = new SQlFun();
                    sqlfun.connection(constr);
                    sqlfun.Sql_open();
                    if (sqlfun.conn.State == ConnectionState.Open)
                    {
                        databasemaxindex = sqlfun.Sql_indexmax("检测结果");
                        databasemaxindex++;
                        sqlfun.Sql_write_checkresult(databasemaxindex, m_passdata);
                        logger.Info(m_passdata.wheeltype + "数据已入数据库");

                        sqlfun.conn.Close();
                    }

                }
            }
            catch (Exception ex)
            {
                logger.Info(ex.Message + ex.StackTrace);
            }
            finally
            {
                m_mainpccomplc.SetBit(m_writedb, 24, 2, true);//发送移位完成信号
            }
        }

        // #13 发送厚度检测顶升高度
        private void ActionThicknessmoveval()
        {
            logger.Info("#13 [" + ConQueue_PassData[2].Item2.ToString() + ", " + ConQueue_PassData[2].Item1.wheeltype);
            try
            {
                double thinknessmov_val = ConQueue_PassData.ElementAt(2).Item1.thinknessmov_val;
                string wheeltype = ConQueue_PassData.ElementAt(2).Item1.wheeltype;

                double regionsize1 = 0.0, regionsize2 = 0.0, regionsize3 = 0.0;
                regionsize3 = m_thicknessZ;
                
                if (HasWheel(wheeltype))
                {
                    regionsize1 = GetWheelParam(wheeltype, "螺栓孔厚度");
                    regionsize2 = GetWheelParam(wheeltype, "后距空间");
                }
                else
                {
                    logger.Error("未查询到 " + wheeltype + " 的配方");
                }

                float result1 = (float)(regionsize3 - regionsize1 - regionsize2 - 423 + thinknessmov_val);
                m_mainpccomplc.SetFloat(m_writedb, 810, result1);
                m_mainpccomplc.SetInt(m_writedb, 814, 666);
                logger.Info(wheeltype + " 螺栓孔厚度工位高度已发送");
            }
            catch (Exception ex)
            {
                logger.Info(ex.ToString());
            }
        }

        // #14 接收厚度检测结果
        private void ActionGetThinknessresult()
        {
            logger.Info("#14 [" + ConQueue_PassData[1].Item2.ToString() + ", " + ConQueue_PassData[1].Item1.wheeltype);
            try
            {
                string wheeltype = ConQueue_PassData.ElementAt(1).Item1.wheeltype;

                double holenum = 0;
                double thickness = 0;
                double toleranceUp = 0;
                double toleranceDown = 0;

                if (HasWheel(wheeltype))
                {
                    holenum = GetWheelParam(wheeltype, "孔数");
                    thickness = GetWheelParam(wheeltype, "螺栓孔厚度");
                    toleranceUp = GetWheelParam(wheeltype, "螺栓孔厚度上公差");
                    toleranceDown = GetWheelParam(wheeltype, "螺栓孔厚度下公差");
                }
                else
                {
                    logger.Error("未查询到 " + wheeltype + " 的配方");
                }

                double[] boltthinkness_val = new double[8];
                double[] boltthinknesscompare_val = new double[8];
                string[] boltthinkness_result = new string[8];

                bool result = true;
                int evalueCount = 0;
                int eindex = -1;
                for (int i = 0; i < holenum; i++)
                {
                    boltthinkness_val[i] = m_mainpccomplc.GetFloat(m_readdb, 280 + i * 4);

                    if (!result)
                    {
                        continue;
                    }

                    double error = boltthinkness_val[i] - thickness;
                    if (boltthinkness_val[i] == 0 || Math.Abs(error) > thickness)
                    {
                        evalueCount += 1;
                        if (evalueCount <= 1)
                        {
                            // 异常值不超过一个时，忽略当前异常
                            error = 0;
                            eindex = i;
                        }
                        else
                        {
                            result = false;
                        }
                    }
                    if (boltthinkness_val[i] == 0 || error > toleranceUp || error < toleranceDown)
                    {
                        result = false;
                    }
                }

                // 检测结果OK，但厚度数据中有且只有一个异常值时，用平均值伪造数据
                if (result && evalueCount == 1 && eindex >= 0)
                {
                    double avg = 0;
                    for (int i = 0; i < holenum; i++)
                    {
                        if (i != eindex)
                        {
                            avg += boltthinkness_val[i];
                        }
                    }
                    avg /= holenum - 1;
                    boltthinkness_val[eindex] = avg;
                }



                for (int i = 0; i < holenum; i++)
                {
                    boltthinknesscompare_val[i] = boltthinkness_val[i] - thickness;

                    if (boltthinknesscompare_val[i] > toleranceUp || boltthinknesscompare_val[i] < toleranceDown)
                    {
                        boltthinkness_result[i] = "NG";
                    }
                    else
                    {
                        boltthinkness_result[i] = "OK";
                        boltthinknesscompare_val[i] = 0;
                    }

                }

                ConQueue_PassData[1].Item1.boltthinkness_val = boltthinkness_val;
                ConQueue_PassData[1].Item1.boltthinknesscompareval = boltthinknesscompare_val;
                ConQueue_PassData[1].Item1.boltthinkness_result = boltthinkness_result;

            }
            catch (Exception ex)
            {
                logger.Info(ex.ToString() + ex.StackTrace);
            }
            finally
            {
            }
        }

        // #15 发送厚度检测开关
        private void ActionGetThinkswitch()
        {
            logger.Info("#15 [" + ConQueue_PassData[2].Item2.ToString() + ", " + ConQueue_PassData[2].Item1.wheeltype);
            try
            {
                double thickSwitch = 0;
                string wheeltype = ConQueue_PassData.ElementAt(2).Item1.wheeltype;
                if (HasWheel(wheeltype))
                {
                    thickSwitch = GetWheelParam(wheeltype, "厚度检测开关");
                }

                m_mainpccomplc.SetInt(m_writedb, 816, (short)Convert.ToInt32(thickSwitch));
            }
            catch (Exception ex)
            {
                logger.Info(ex.ToString());
            }
        }

        // #16 发送螺栓孔数据
        private void ActionBoltholedata()
        {
            logger.Info("#16 [" + ConQueue_PassData[1].Item2.ToString() + ", " + ConQueue_PassData[1].Item1.wheeltype);

            try
            {
                string wheeltype = ConQueue_PassData.ElementAt(1).Item1.wheeltype;
                double backDistance = 0;
                double height = 0;
                double servo = 0;
                double thickness = 0;
                double holeNum = 0;

                if (HasWheel(wheeltype))
                {
                    backDistance = GetWheelParam(wheeltype, "后距空间");
                    height = GetWheelParam(wheeltype, "总高");
                    servo = GetWheelParam(wheeltype, "螺栓孔厚度工位伺服移动");
                    thickness = GetWheelParam(wheeltype, "螺栓孔厚度");
                    holeNum = GetWheelParam(wheeltype, "孔数");
                }
                m_mainpccomplc.SetFloat(m_writedb, 554, (float)backDistance);
                m_mainpccomplc.SetFloat(m_writedb, 558, (float)height);
                m_mainpccomplc.SetFloat(m_writedb, 562, (float)servo);
                m_mainpccomplc.SetFloat(m_writedb, 824, (float)thickness);

                short tmp = (short)Convert.ToInt32(holeNum);
                m_mainpccomplc.SetInt(m_writedb, 828, tmp);
                logger.Info("hole num " + tmp);

                m_mainpccomplc.SetString(m_writedb, 566, wheeltype);
                m_mainpccomplc.SetInt(m_writedb, 822, 666);
                logger.Info("已写入螺栓孔数据");
            }
            catch (Exception e)
            {
                logger.Error(e.Message + e.StackTrace);
            }
        }

        
        //工控机接收块在前，发送块在后
        private void GetPlcData(string ip, int Rack, int Slot, int readdb, int writedb)
        {
            SQlFun m_sqlfun = new SQlFun();

            m_ip = ip;
            m_readdb = readdb;
            m_writedb = writedb;
            m_Rack = Rack;
            m_Slot = Slot;

            bool m_bit0 = false, m_bit1 = false, m_bit2 = false, m_bit3 = false, m_bit4 = false;
            bool m_bit5 = false, m_bit6 = false, m_bit7 = false, m_bit8 = false, m_bit9 = false;
            bool m_bit10 = false, m_bit11 = false, m_bit12 = false, m_bit13 = false, m_bit14 = false;
            bool m_bit15 = false;
            bool m_bit16 = false;
            ReadPlcData m_readplcdata = new ReadPlcData();//存储读取的PLC数据

            int connection = m_mainpccomplc.Connect(m_ip, m_Rack, m_Slot);
            if (connection == 0)
            {
                m_plcstatus = true;
                mPLCReonnCount = 0;
                s7commun.tB_Status.BackColor = Color.Green;

                while (m_plcstatus)
                {
                    try
                    {

                        #region 读取数据
                        Thread.Sleep(10);
                        connection = m_mainpccomplc.Connect(m_ip, m_Rack, m_Slot);
                        if (connection != 0)
                        {
                            MessageBoxEX.Show(res.GetString("PLC_disconnected"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                            m_plcstatus = false;
                            s7commun.tB_Status.BackColor = Color.Red;
                            s7commun.TextError.Text = res.GetString("disconnected");
                            s7commun.DisconnectBtn.Enabled = false;
                            s7commun.ConnectBtn.Enabled = true;
                            break;
                        }
                        Thread.Sleep(20);
                        m_readplcdata.get_wheeltype = m_mainpccomplc.Getbit(m_readdb, 0, 0);//是否可以取轮毂型号

                        m_readplcdata.get_lasermovval = m_mainpccomplc.Getbit(m_readdb, 0, 1); //是否可以取深度检测的顶升高度

                        m_readplcdata.get_deepval = m_mainpccomplc.Getbit(m_readdb, 0, 2);//是否可以取深度值

                        m_readplcdata.get_cenholmovval = m_mainpccomplc.Getbit(m_readdb, 0, 3);//是否可以取中心孔顶升高度

                        m_readplcdata.cenholetrigger = m_mainpccomplc.Getbit(m_readdb, 0, 4);//是否中心孔工位拍照

                        m_readplcdata.get_hatholmoveval = m_mainpccomplc.Getbit(m_readdb, 0, 5);//是否可以取帽止口的顶升高度

                        m_readplcdata.hatholetrigger = m_mainpccomplc.Getbit(m_readdb, 0, 6);//是否可以帽止口触发

                        m_readplcdata.get_markval = m_mainpccomplc.Getbit(m_readdb, 0, 7);//是否可以取标记顶升高度

                        m_readplcdata.get_datamove = m_mainpccomplc.Getbit(m_readdb, 1, 0);//数据是否可以挪位与入库    

                        m_readplcdata.wheeltrigger = m_mainpccomplc.Getbit(m_readdb, 1, 1);//是否轮型识别工位拍照触发

                        m_readplcdata.lasertrigger = m_mainpccomplc.Getbit(m_readdb, 1, 2);//是否轮激光拍照触发

                        m_readplcdata.get_pretreatmentval = m_mainpccomplc.Getbit(m_readdb, 1, 3);//是否可以取预处理的值

                        m_readplcdata.get_thicknessmoveval = m_mainpccomplc.Getbit(m_readdb, 1, 4);//是否可以取厚度检测的顶升高度

                        m_readplcdata.get_thinknessresult = m_mainpccomplc.Getbit(m_readdb, 1, 5);//是否可以取厚度检测的结果

                        m_readplcdata.get_thinknessswitch = m_mainpccomplc.Getbit(m_readdb, 1, 6);//是否可以取厚度检测开关的值

                        m_readplcdata.set_boltholedata = m_mainpccomplc.Getbit(m_readdb, 278, 0);//是否可以发送螺栓孔数据

                        Thread.Sleep(10);
                        #endregion

                        #region 0接收轮毂型号
                        //接收轮毂型号

                        if (m_readplcdata.get_wheeltype && (!m_bit0))
                        {
                            logger.Info("#0");
                            try
                            {
                                PassData m_passdata = new PassData();
                                m_bit0 = true;

                                m_readplcdata.wheeltype = m_mainpccomplc.GetString(m_readdb, 22);//轮毂型号
                                m_passdata.wheeltype = m_mainpccomplc.GetString(m_readdb, 22);//轮毂型号

                                logger.Info("#0 轮型 [" + _id.ToString() + "," + m_passdata.wheeltype);

                                m_sqlfun.connection(constr);
                                m_sqlfun.Sql_open();
                                if (m_sqlfun.conn.State == ConnectionState.Open)
                                {
                                    string typename = m_readplcdata.wheeltype;
                                    bool exist = m_sqlfun.Sql_ExistColumn("轮毂参数", "轮毂型号", typename);

                                    if (exist)
                                    {
                                        double regionsize;
                                        m_sqlfun.Sql_Find("深度检测工位伺服移动", "轮毂参数", typename, out regionsize);
                                        m_passdata.lasermov_val = regionsize;
                                        m_sqlfun.Sql_Find("深度检测工位伺服水平移动", "轮毂参数", typename, out regionsize);
                                        m_passdata.lasermovH_val = regionsize;
                                        m_sqlfun.Sql_Find("中心孔工位伺服移动", "轮毂参数", typename, out regionsize);
                                        m_passdata.cenholmov_val = regionsize;
                                        m_sqlfun.Sql_Find("帽止口工位伺服移动", "轮毂参数", typename, out regionsize);
                                        m_passdata.hatholmov_val = regionsize;
                                        m_sqlfun.Sql_Find("标记工位伺服移动", "轮毂参数", typename, out regionsize);
                                        m_passdata.markmov_val = regionsize;
                                        m_sqlfun.Sql_Find("螺栓孔厚度工位伺服移动", "轮毂参数", typename, out regionsize);
                                        m_passdata.thinknessmov_val = regionsize;



                                        m_sqlfun.Sql_Find("帽槽深度", "轮毂参数", typename, out regionsize);
                                        m_passdata.deep_stdval = regionsize;
                                        m_sqlfun.Sql_Find("帽槽深度上公差", "轮毂参数", typename, out regionsize);
                                        m_passdata.deeptolp_stdval = regionsize;
                                        m_sqlfun.Sql_Find("帽槽深度下公差", "轮毂参数", typename, out regionsize);
                                        m_passdata.deeptoln_stdval = regionsize;

                                        m_sqlfun.Sql_Find("中心孔直径", "轮毂参数", typename, out regionsize);
                                        m_passdata.cenhol_stdval = regionsize;
                                        m_sqlfun.Sql_Find("中心孔直径上公差", "轮毂参数", typename, out regionsize);
                                        m_passdata.cenholtolp_stdval = regionsize;
                                        m_sqlfun.Sql_Find("中心孔直径下公差", "轮毂参数", typename, out regionsize);
                                        m_passdata.cenholtoln_stdval = regionsize;


                                        m_sqlfun.Sql_Find("孔数", "轮毂参数", typename, out regionsize);
                                        m_passdata.hole_num = (int)regionsize;

                                        m_sqlfun.Sql_Find("PCD节圆直径", "轮毂参数", typename, out regionsize);
                                        m_passdata.PCD_stdval = regionsize;

                                        m_sqlfun.conn.Close();

                                    }
                                    else
                                    {
                                        logger.Error("未查询到 " + typename + " 的配方");
                                    }

                                    //else
                                    //    logger.Info("无此轮毂信息");


                                    if (ConQueue_PassData.Count == 5)
                                    {

                                        ConQueue_PassData[4] = new Tuple<PassData, int>(m_passdata, _id++);//进入队列
                                        if (m_passdata.wheeltype != "null")
                                            logger.Info("取" + m_passdata.wheeltype + "的配方成功");
                                        else
                                            logger.Info("无轮毂，录入空配方");
                                    }
                                    else
                                    {

                                        logger.Info("超队列数目预设，入队失败！");
                                    }

                                }
                            }
                            catch (Exception e)
                            {
                                logger.Error(e.Message + e.StackTrace);
                            }



                        }


                        if (!m_readplcdata.get_wheeltype)
                        {
                            m_bit0 = false;
                        }


                        #endregion

                        #region 1发送获得激光工位移动高度
                        if (m_readplcdata.get_lasermovval && (!m_bit1))
                        {
                            logger.Info("#1 [" + ConQueue_PassData[4].Item2.ToString() + ", " + ConQueue_PassData[4].Item1.wheeltype);
                            m_bit1 = true;
                            if (ConQueue_PassData.Count >= 5)
                            {

                                if (ConQueue_PassData.ElementAt(4).Item1.wheeltype == "NG")
                                {
                                    m_mainpccomplc.SetFloat(m_writedb, 0, 0);
                                    m_mainpccomplc.SetFloat(m_writedb, 830, 0);
                                    logger.Info("深度检测工位高度已发送");
                                }
                                else
                                {
                                    double lasermov_val = ConQueue_PassData.ElementAt(4).Item1.lasermov_val;
                                    double lasermovH_val = ConQueue_PassData.ElementAt(4).Item1.lasermovH_val;
                                    string str = ConQueue_PassData.ElementAt(4).Item1.wheeltype;
                                    double regionsize1 = 0.0, regionsize2 = 0.0, regionsize3 = 0.0, regionsize4 = 0.0;
                                    regionsize3 = m_deepZ;
                                    regionsize4 = m_deepX;
                                    m_sqlfun.connection(constr);
                                    m_sqlfun.Sql_open();
                                    if (m_sqlfun.conn.State == ConnectionState.Open)
                                    {

                                        m_sqlfun.Sql_Find("轮芯厚度", "轮毂参数", str, out regionsize1);
                                        m_sqlfun.Sql_Find("后距空间", "轮毂参数", str, out regionsize2);

                                    }
                                    m_sqlfun.conn.Close();

                                    float result1 = (float)(regionsize3 - regionsize1 - regionsize2 - 120 + lasermov_val);
                                    m_mainpccomplc.SetFloat(m_writedb, 0, result1);
                                    m_mainpccomplc.SetFloat(m_writedb, 830, (float)(regionsize4 + lasermovH_val));
                                    logger.Info(str + "深度检测工位高度已发送 " + result1.ToString() + ", " + (regionsize4 + lasermovH_val).ToString());
                                }

                                //if (str != null)  
                                //m_mainpccomplc.SetString(m_writedb, 554, str);
                                m_mainpccomplc.SetInt(m_writedb, 4, 666);

                            }
                            else
                            {
                                m_mainpccomplc.SetFloat(m_writedb, 0, 100);
                                m_mainpccomplc.SetFloat(m_writedb, 830, 50);
                                // m_mainpccomplc.SetString(m_writedb, 554, "");
                                m_mainpccomplc.SetInt(m_writedb, 4, 999);
                                logger.Info("队列不完整，当前队列数量为" + ConQueue_PassData.Count.ToString());
                            }

                            //textBox_message.Text = str+"深度检测工位高度已发送\n\r";


                        }
                        if (!m_readplcdata.get_lasermovval)
                        {
                            m_bit1 = false;
                        }
                        #endregion

                        #region 2接收深度值
                        //接收深度值
                        if (m_readplcdata.get_deepval && (!m_bit2))
                        {
                            logger.Info("#2 [" + ConQueue_PassData[4].Item2.ToString() + ", " + ConQueue_PassData[4].Item1.wheeltype);
                            m_bit2 = true;

                            m_readplcdata.deepval1 = m_mainpccomplc.GetFloat(m_readdb, 2);//深度值
                            m_readplcdata.deepval2 = m_mainpccomplc.GetFloat(m_readdb, 6);//深度值

                            //截取小数点后面2位
                            string t_str = m_readplcdata.deepval1.ToString("f2");
                            m_readplcdata.deepval1 = double.Parse(t_str);
                            t_str = m_readplcdata.deepval2.ToString("f2");
                            m_readplcdata.deepval2 = double.Parse(t_str);

                            double[] m_measureval = { m_readplcdata.deepval1, m_readplcdata.deepval2 };
                            ConQueue_PassData.ElementAt(4).Item1.laserdeep_val = m_measureval;

                            string str = ConQueue_PassData.ElementAt(4).Item1.wheeltype;


                            Caliper m_caliper = new Caliper();
                            double resultval; string result;
                            m_caliper.LaserDeep_CompareDesignVal(constr, str, ConQueue_PassData.ElementAt(4).Item1.laserdeep_val, out resultval, out result);
                            ConQueue_PassData.ElementAt(4).Item1.lasercompareval = resultval;
                            ConQueue_PassData.ElementAt(4).Item1.deep_result = result;

                            logger.Info(str + "深度检测值已接收\n\r");


                        }
                        if (!m_readplcdata.get_deepval)
                        {
                            m_bit2 = false;
                        }
                        #endregion

                        #region 3发送中心孔工位移动高度
                        if (m_readplcdata.get_cenholmovval && (!m_bit3))
                        {
                            logger.Info("#3 [" + ConQueue_PassData[3].Item2.ToString() + ", " + ConQueue_PassData[3].Item1.wheeltype);
                            m_bit3 = true;
                            if (ConQueue_PassData.Count >= 4)
                            {
                                double lasermov_val = ConQueue_PassData.ElementAt(3).Item1.cenholmov_val;
                                string str = ConQueue_PassData.ElementAt(3).Item1.wheeltype;
                                double regionsize1 = 0.0, regionsize2 = 0.0, regionsize3 = 0.0;
                                double light = 0.0;
                                regionsize3 = m_centreZ;

                                try
                                {
                                    if (str == "NG")
                                    {
                                        m_mainpccomplc.SetFloat(m_writedb, 6, 0);
                                        logger.Info("中心孔工位高度已发送");
                                    }
                                    else
                                    {
                                        m_sqlfun.connection(constr);
                                        m_sqlfun.Sql_open();
                                        if (m_sqlfun.conn.State == ConnectionState.Open)
                                        {
                                            m_sqlfun.Sql_Find("后距空间", "轮毂参数", str, out regionsize1);
                                            try
                                            {
                                                m_sqlfun.Sql_Find("中心孔环光开关", "轮毂参数", str, out light);
                                            }
                                            catch
                                            {
                                            }
                                        }
                                        m_sqlfun.conn.Close();

                                        float result1 = (float)(410 - regionsize1 - regionsize3 - lasermov_val);

                                        m_mainpccomplc.SetFloat(m_writedb, 6, result1);
                                        m_mainpccomplc.SetBit(m_writedb, 834, 0, (int)light != 0);
                                        logger.Info(str + "中心孔工位高度已发送 " + result1.ToString());
                                    }
                                    m_mainpccomplc.SetInt(m_writedb, 10, 666);
                                }
                                catch (Exception e)
                                {
                                    logger.Info("轮型 [" + str + "]");
                                    logger.Error(e.Message + e.StackTrace);
                                    m_mainpccomplc.SetFloat(m_writedb, 6, (float)-30);
                                    m_mainpccomplc.SetInt(m_writedb, 10, 999);
                                }

                                string wheeltype = ConQueue_PassData.ElementAt(3).Item1.wheeltype;
                                double exposureTime = 0;
                                try
                                {
                                    m_sqlfun.connection(constr);
                                    m_sqlfun.Sql_open();
                                    if (m_sqlfun.conn.State == ConnectionState.Open)
                                    {
                                        m_sqlfun.Sql_Find("中心孔曝光时长", "轮毂参数", wheeltype, out exposureTime);
                                    }
                                    m_sqlfun.conn.Close();
                                }
                                catch (Exception e)
                                {
                                    logger.Warn(e.Message + e.StackTrace);
                                    exposureTime = 0;
                                }
                                if (exposureTime <= 0)
                                {
                                    exposureTime = m_defExposureTimeCen;
                                }
                                logger.Info(wheeltype + " cenhole exposure: " + exposureTime);

                                if (m_cameralink)
                                {
                                    m_HKCameraLink1.SetExposure((float)exposureTime);
                                }
                                else
                                {
                                    m_HKCamera2.SetExposure((float)exposureTime);
                                }
                            }
                            else
                            {
                                m_mainpccomplc.SetFloat(m_writedb, 6, (float)-30);
                                //m_mainpccomplc.SetString(m_writedb, 554, "");
                                m_mainpccomplc.SetInt(m_writedb, 10, 999);

                                logger.Info("队列不完整，当前队列数量为" + ConQueue_PassData.Count.ToString());
                            }

                            // textBox_message.Text = str+"中心孔工位高度已发送\n\r";


                        }
                        if (!m_readplcdata.get_cenholmovval)
                        {
                            m_bit3 = false;
                        }
                        #endregion


                        #region 4中心孔相机触发拍照
                        if (m_readplcdata.cenholetrigger && (!m_bit4))
                        {
                            logger.Info("#4");
                            m_bit4 = true;

                            SoftwareExecute2();


                        }
                        if (!m_readplcdata.cenholetrigger)
                        {
                            m_bit4 = false;
                        }

                        #endregion

                        #region 5发送帽止口顶升高度

                        //发送帽止口顶升高度
                        if (m_readplcdata.get_hatholmoveval && (!m_bit5))
                        {
                            logger.Info("#5 [" + ConQueue_PassData[2].Item2.ToString() + ", " + ConQueue_PassData[2].Item1.wheeltype);
                            m_bit5 = true;
                            if (ConQueue_PassData.Count >= 3)
                            {
                                if (string.IsNullOrEmpty(ConQueue_PassData.ElementAt(2).Item1.wheeltype) || ConQueue_PassData.ElementAt(2).Item1.wheeltype == "NG")
                                {
                                    m_mainpccomplc.SetFloat(m_writedb, 12, 0);
                                    logger.Info("帽止口检测工位高度已发送");
                                }
                                else
                                {
                                    double lasermov_val = ConQueue_PassData.ElementAt(2).Item1.hatholmov_val;
                                    string str = ConQueue_PassData.ElementAt(2).Item1.wheeltype;

                                    double regionsize1 = 0.0, regionsize2 = 0.0, regionsize3 = 0.0;
                                    double frontLight = 0.0;
                                    regionsize3 = m_hatZ;
                                    m_sqlfun.connection(constr);
                                    m_sqlfun.Sql_open();
                                    if (m_sqlfun.conn.State == ConnectionState.Open)
                                    {
                                        m_sqlfun.Sql_Find("轮芯厚度", "轮毂参数", str, out regionsize1);
                                        m_sqlfun.Sql_Find("后距空间", "轮毂参数", str, out regionsize2);
                                        try
                                        {
                                            m_sqlfun.Sql_Find("帽止口正面光开关", "轮毂参数", str, out frontLight);
                                        }
                                        catch
                                        {
                                        }

                                    }
                                    m_sqlfun.conn.Close();

                                    float result1 = (float)(regionsize3 - regionsize1 - regionsize2 - m_hatFocal + lasermov_val);


                                    m_mainpccomplc.SetFloat(m_writedb, 12, (float)result1);
                                    m_mainpccomplc.SetBit(m_writedb, 24, 6, (int)frontLight != 0);
                                    logger.Info(str + "帽止口检测工位高度已发送 " + result1.ToString());
                                }

                                m_mainpccomplc.SetInt(m_writedb, 16, 666);
                            }

                            else
                            {
                                m_mainpccomplc.SetFloat(m_writedb, 12, (float)100);
                                //m_mainpccomplc.SetString(m_writedb, 554, "");
                                m_mainpccomplc.SetInt(m_writedb, 16, 999);
                                logger.Info("队列不完整，当前队列数量为" + ConQueue_PassData.Count.ToString());

                            }

                            string wheeltype = ConQueue_PassData.ElementAt(2).Item1.wheeltype;
                            double exposureTime = 0;
                            try
                            {
                                m_sqlfun.connection(constr);
                                m_sqlfun.Sql_open();
                                if (m_sqlfun.conn.State == ConnectionState.Open)
                                {
                                    m_sqlfun.Sql_Find("帽止口曝光时长", "轮毂参数", wheeltype, out exposureTime);
                                }
                                m_sqlfun.conn.Close();
                            }
                            catch (Exception e)
                            {
                                logger.Warn(e.Message + e.StackTrace);
                                exposureTime = 0;
                            }
                            if (exposureTime <= 0)
                            {
                                exposureTime = m_defExposureTimeHat;
                            }
                            logger.Info(wheeltype + " hathole exposure: " + exposureTime);

                            m_HKCamera3.SetExposure((float)exposureTime);

                        }
                        if (!m_readplcdata.get_hatholmoveval)
                        {
                            m_bit5 = false;
                        }

                        #endregion

                        #region 6帽止口相机触发拍照

                        if (m_readplcdata.hatholetrigger && (!m_bit6))
                        {
                            logger.Info("#6");
                            m_bit6 = true;


                            SoftwareExecute3();



                        }
                        if (!m_readplcdata.hatholetrigger)
                        {
                            m_bit6 = false;

                        }

                        #endregion

                        #region 7是否可以取标记得高度
                        if (m_readplcdata.get_markval && (!m_bit7))
                        {
                            logger.Info("#7 [" + ConQueue_PassData[0].Item2.ToString() + ", " + ConQueue_PassData[0].Item1.wheeltype);

                            try
                            {
                                m_bit7 = true;
                                double thickSwitch = 0;
                                if (ConQueue_PassData.ElementAt(0).Item1.wheeltype == "NG")
                                {
                                    m_mainpccomplc.SetFloat(m_writedb, 18, 0);
                                    logger.Info("打钢印工位高度已发送");
                                }
                                else
                                {
                                    double lasermov_val = ConQueue_PassData.ElementAt(0).Item1.markmov_val;
                                    string str = ConQueue_PassData.ElementAt(0).Item1.wheeltype;

                                    double regionsize1 = 0.0, regionsize2 = 0.0, regionsize3 = 0.0;
                                    regionsize3 = m_markZ;
                                    m_sqlfun.connection(constr);
                                    m_sqlfun.Sql_open();
                                    if (m_sqlfun.conn.State == ConnectionState.Open)
                                    {
                                        m_sqlfun.Sql_Find("总高", "轮毂参数", str, out regionsize1);
                                        m_sqlfun.Sql_Find("厚度检测开关", "轮毂参数", str, out thickSwitch);
                                    }
                                    m_sqlfun.conn.Close();

                                    float result1 = -(float)(regionsize3 - regionsize1 + 56 + lasermov_val);

                                    m_mainpccomplc.SetFloat(m_writedb, 18, (float)result1);
                                    logger.Info(str + "打钢印工位高度已发送 " + result1.ToString());
                                }


                                //最终结果判断
                                string rel3 = ConQueue_PassData[0].Item1.deep_result;
                                string rel2 = ConQueue_PassData[0].Item1.cenhol_result;
                                string[] rel1 = ConQueue_PassData[0].Item1.pos_result;
                                string[] rel4 = ConQueue_PassData[0].Item1.boltdiameter_result;
                                string[] rel5 = ConQueue_PassData[0].Item1.boltthinkness_result;
                                string rel0 = ConQueue_PassData[0].Item1.hathol_result;

                                List<string> rel = new List<string>();
                                rel.Add(rel3);
                                rel.Add(rel2);
                                for (int i = 0; i < ConQueue_PassData[0].Item1.hole_num; i++)
                                {
                                    rel.Add(rel1[i]);
                                }
                                for (int i = 0; i < ConQueue_PassData[0].Item1.hole_num; i++)
                                {
                                    rel.Add(rel4[i]);
                                }

                                if (thickSwitch != 0)
                                {
                                    for (int i = 0; i < ConQueue_PassData[0].Item1.hole_num; i++)
                                    {
                                        rel.Add(rel5[i]);
                                    }
                                }
                                rel.Add(rel0);
                                

                                if (ConQueue_PassData[0].Item1.wheeltype != "empty")
                                {
                                    if ((rel.Contains("NG")) || (rel.Contains(null)))
                                    {

                                        ConQueue_PassData[0].Item1.last_result = "NG";

                                    }
                                    else
                                    {
                                        ConQueue_PassData[0].Item1.last_result = "OK";
                                    }
                                }
                                else
                                {
                                    ConQueue_PassData[0].Item1.last_result = "NG";
                                }

                                //发送检测最后结果
                                m_mainpccomplc.SetString(m_writedb, 298, ConQueue_PassData[0].Item1.last_result);
                                m_mainpccomplc.SetString(m_writedb, 836, ConQueue_PassData[0].Item1.wheeltype);

                                byte ngPos = 0;
                                ngPos = (byte)(ngPos | (ConQueue_PassData[0].Item1.wheeltype.Equals("NG") ? 1 : 0)              << 6);
                                ngPos = (byte)(ngPos | (ConQueue_PassData[0].Item1.deep_result.Equals("NG") ? 1 : 0)            << 5);
                                ngPos = (byte)(ngPos | (ConQueue_PassData[0].Item1.cenhol_result.Equals("NG") ? 1 : 0)          << 4);
                                ngPos = (byte)(ngPos | (ConQueue_PassData[0].Item1.boltdiameter_result.Contains("NG") ? 1 : 1)  << 3);
                                ngPos = (byte)(ngPos | (ConQueue_PassData[0].Item1.pos_result.Contains("NG") ? 1 : 0)           << 2);
                                ngPos = (byte)(ngPos | (ConQueue_PassData[0].Item1.hathol_result.Equals("NG") ? 1 : 0)          << 1);
                                ngPos = (byte)(ngPos | (ConQueue_PassData[0].Item1.boltthinkness_result.Contains("NG") ? 1 : 1) << 0);
                                m_mainpccomplc.SetByte(m_writedb, 1092, ngPos);
                                
                                m_mainpccomplc.SetInt(m_writedb, 22, 666);
                                logger.Error(ConQueue_PassData[0].Item1.wheeltype + " [" + ConQueue_PassData[0].Item2.ToString() + " 最终结果已发送");
                            }
                            catch (Exception ex)
                            {
                                logger.Info(ex.ToString());
                                m_mainpccomplc.SetInt(m_writedb, 22, 999);
                            }

                        }
                        if (!m_readplcdata.get_markval)
                        {
                            m_bit7 = false;
                        }

                        #endregion

                        #region 8数据允许传递
                        if (m_readplcdata.get_datamove && (!m_bit8))
                        {
                            logger.Info("#8 [" + ConQueue_PassData[0].Item2);
                            m_bit8 = true;
                            try
                            {
                                PassData m_passdata = new PassData();
                                PassData m_nullpassdata = new PassData();
                                if (ConQueue_PassData.Count == 5)
                                {
                                    m_passdata = ConQueue_PassData[0].Item1;
                                    if (m_MEGAPHASE)
                                    {
                                        ConQueue_PassData[0] = ConQueue_PassData[1];
                                        ConQueue_PassData[1] = ConQueue_PassData[2];
                                    }
                                    else
                                    {
                                        ConQueue_PassData[0] = ConQueue_PassData[2];
                                    }
                                    ConQueue_PassData[2] = ConQueue_PassData[3];
                                    ConQueue_PassData[3] = ConQueue_PassData[4];
                                    ConQueue_PassData[4] = new Tuple<PassData, int>(m_nullpassdata, -1);

                                    if ("".Equals(m_passdata.last_result))
                                    {
                                        m_passdata.last_result = "NG";
                                    }
                                    m_sqlfun.connection(constr);
                                    m_sqlfun.Sql_open();
                                    if (m_sqlfun.conn.State == ConnectionState.Open)
                                    {
                                        databasemaxindex = m_sqlfun.Sql_indexmax("检测结果");
                                        databasemaxindex++;
                                        if (m_passdata.wheeltype != "empty")
                                        {
                                            if (!string.IsNullOrEmpty(m_passdata.wheeltype))
                                            {
                                                // s = "螺栓孔厚度前3:"+ m_passdata.boltthinkness_val[0].ToString()+ m_passdata.boltthinkness_val[1].ToString()+ m_passdata.boltthinkness_val[2].ToString();
                                                m_sqlfun.Sql_write_checkresult(databasemaxindex, m_passdata);
                                            }
                                        }

                                        m_sqlfun.conn.Close();
                                    }

                                    logger.Info(m_passdata.wheeltype + "数据已入数据库");

                                }
                                else
                                {

                                    logger.Info("队列数量小于预设，存储失败");
                                }

                            }
                            catch (Exception ex)
                            {

                                logger.Info(ex.Message + ex.StackTrace);
                            }
                            finally
                            {
                                m_mainpccomplc.SetBit(m_writedb, 24, 2, true);//发送移位完成信号
                            }




                        }
                        if (!m_readplcdata.get_datamove)
                        {
                            m_bit8 = false;
                        }

                        #endregion

                        #region 9轮型识别相机触发拍照
                        if (m_readplcdata.wheeltrigger && (!m_bit9))
                        {
                            C1CaptureCount++;
                            logger.Info("#9  C1拍照 " + C1CaptureCount.ToString());
                            m_bit9 = true;

                            m_HKCamera1.SoftTrigger();


                        }
                        if (!m_readplcdata.wheeltrigger)
                        {
                            m_bit9 = false;
                        }

                        #endregion

                        #region 10激光相机触发拍照
                        if (m_readplcdata.lasertrigger && (!m_bit10))
                        {
                            logger.Info("#10");
                            m_bit10 = true;

                            SoftwareExecute4();


                        }
                        if (!m_readplcdata.lasertrigger)
                        {
                            m_bit10 = false;
                        }
                        #endregion

                        #region 11获得预处理的数据
                        if (m_readplcdata.get_pretreatmentval && (!m_bit11))
                        {
                            logger.Info("#11");
                            m_bit11 = true;

                            logger.Info("取预处理的数据成功");


                        }
                        if (!m_readplcdata.get_pretreatmentval)
                        {
                            m_bit11 = false;
                        }
                        #endregion

                        #region 13发送厚度检测顶升高度并将轮毂型号写入数据交互区10.18增加异常机制
                        if (m_readplcdata.get_thicknessmoveval && (!m_bit13))
                        {
                            logger.Info("#13 [" + ConQueue_PassData[2].Item2.ToString() + ", " + ConQueue_PassData[2].Item1.wheeltype);
                            m_bit13 = true;
                            try
                            {
                                if (ConQueue_PassData.Count >= 5)
                                {
                                    double thinknessmov_val = ConQueue_PassData.ElementAt(2).Item1.thinknessmov_val;
                                    string str = ConQueue_PassData.ElementAt(2).Item1.wheeltype;

                                    double regionsize1 = 0.0, regionsize2 = 0.0, regionsize3 = 0.0;
                                    regionsize3 = m_thicknessZ;
                                    m_sqlfun.connection(constr);
                                    m_sqlfun.Sql_open();
                                    if (m_sqlfun.conn.State == ConnectionState.Open)
                                    {
                                        m_sqlfun.Sql_Find("螺栓孔厚度", "轮毂参数", str, out regionsize1);
                                        m_sqlfun.Sql_Find("后距空间", "轮毂参数", str, out regionsize2);
                                    }
                                    m_sqlfun.conn.Close();


                                    float result1 = (float)(regionsize3 - regionsize1 - regionsize2 - 423 + thinknessmov_val);
                                    m_mainpccomplc.SetFloat(m_writedb, 810, result1);

                                    //if (str != null)
                                    //    m_mainpccomplc.SetString(m_writedb, 554, str);
                                    m_mainpccomplc.SetInt(m_writedb, 814, 666);

                                    logger.Info(str + "螺栓孔厚度工位高度已发送");
                                }

                                else
                                {
                                    m_mainpccomplc.SetFloat(m_writedb, 810, (float)100);
                                    //m_mainpccomplc.SetString(m_writedb, 554, "");
                                    m_mainpccomplc.SetInt(m_writedb, 814, 999);
                                    logger.Info("队列不完整，当前队列数量为" + ConQueue_PassData.Count.ToString());

                                }
                                //数据写入暂存区
                                m_sqlfun.connection(constr);
                                m_sqlfun.Sql_open();
                                if (m_sqlfun.conn.State == ConnectionState.Open)
                                {
                                    string str = ConQueue_PassData.ElementAt(2).Item1.wheeltype;
                                    m_sqlfun.Sql_cleartable("软件数据交互");
                                    m_sqlfun.Sql_write_wheeltypetotemporaryarea("软件数据交互", str);
                                    logger.Info(str + "型号已写入暂存");
                                    m_sqlfun.conn.Close();

                                }
                            }
                            catch (Exception ex)
                            {
                                logger.Info(ex.ToString());
                            }
                            finally
                            {
                                m_mainpccomplc.SetBit(202, 0, 6, true);

                            }



                        }
                        if (!m_readplcdata.get_thicknessmoveval)
                        {
                            m_bit13 = false;
                        }
                        #endregion

                        #region 14取厚度检测结果
                        if (m_readplcdata.get_thinknessresult && (!m_bit14))
                        {
                            logger.Info("#14 [" + ConQueue_PassData[1].Item2.ToString() + ", " + ConQueue_PassData[1].Item1.wheeltype);
                            m_bit14 = true;
                            try
                            {
                                string wheeltype = ConQueue_PassData.ElementAt(1).Item1.wheeltype;

                                double holenum = 0;
                                double thickness = 0;
                                double toleranceUp = 0;
                                double toleranceDown = 0;

                                m_sqlfun.connection(constr);
                                m_sqlfun.Sql_open();
                                if (m_sqlfun.conn.State == ConnectionState.Open)
                                {
                                    if (m_sqlfun.Sql_ExistColumn("轮毂参数", "轮毂型号", wheeltype))
                                    {
                                        m_sqlfun.Sql_Find("孔数", "轮毂参数", wheeltype, out holenum);
                                        m_sqlfun.Sql_Find("螺栓孔厚度", "轮毂参数", wheeltype, out thickness);
                                        m_sqlfun.Sql_Find("螺栓孔厚度上公差", "轮毂参数", wheeltype, out toleranceUp);
                                        m_sqlfun.Sql_Find("螺栓孔厚度下公差", "轮毂参数", wheeltype, out toleranceDown);
                                    }
                                    m_sqlfun.conn.Close();
                                }

                                double[] boltthinkness_val = new double[8];
                                double[] boltthinknesscompare_val = new double[8];
                                string[] boltthinkness_result = new string[8];

                                bool result = true;
                                int evalueCount = 0;
                                int eindex = -1;
                                for (int i = 0; i < holenum; i++)
                                {
                                    boltthinkness_val[i] = m_mainpccomplc.GetFloat(m_readdb, 280 + i * 4);

                                    if(!result)
                                    {
                                        continue;
                                    }

                                    double error = boltthinkness_val[i] - thickness;
                                    if (boltthinkness_val[i] == 0 || Math.Abs(error) > thickness)
                                    {
                                        evalueCount += 1;
                                        if (evalueCount <= 1)
                                        {
                                            // 异常值不超过一个时，忽略当前异常
                                            error = 0;
                                            eindex = i;
                                        }
                                        else
                                        {
                                            result = false;
                                        }
                                    }
                                    if (boltthinkness_val[i] == 0 || error > toleranceUp || error < toleranceDown)
                                    {
                                        result = false;
                                    }
                                }

                                // 检测结果OK，但厚度数据中有且只有一个异常值时，用平均值伪造数据
                                if (result && evalueCount == 1 && eindex >= 0)
                                {
                                    double avg = 0;
                                    for (int i = 0; i < holenum; i++)
                                    {
                                        if (i != eindex)
                                        {
                                            avg += boltthinkness_val[i];
                                        }
                                    }
                                    avg /= holenum - 1;
                                    boltthinkness_val[eindex] = avg;
                                }



                                for (int i = 0; i < holenum; i++)
                                {
                                    boltthinknesscompare_val[i] = boltthinkness_val[i] - thickness;

                                    if (boltthinknesscompare_val[i] > toleranceUp || boltthinknesscompare_val[i] < toleranceDown)
                                    {
                                        boltthinkness_result[i] = "NG";
                                    }
                                    else
                                    {
                                        boltthinkness_result[i] = "OK";
                                        boltthinknesscompare_val[i] = 0;
                                    }

                                }

                                ConQueue_PassData[1].Item1.boltthinkness_val = boltthinkness_val;
                                ConQueue_PassData[1].Item1.boltthinknesscompareval = boltthinknesscompare_val;
                                ConQueue_PassData[1].Item1.boltthinkness_result = boltthinkness_result;

                            }
                            catch (Exception ex)
                            {
                                logger.Info(ex.ToString() + ex.StackTrace);
                            }
                            finally
                            {
                                //m_mainpccomplc.SetBit(m_writedb, 24, 5, true);//取暂存区数据成功

                            }
                        }
                        if (!m_readplcdata.get_thinknessresult)
                        {
                            m_bit14 = false;
                        }
                        #endregion

                        #region 15取厚度检测开关的值
                        if (m_readplcdata.get_thinknessswitch && (!m_bit15))
                        {

                            logger.Info("#15 [" + ConQueue_PassData[2].Item2.ToString() + ", " + ConQueue_PassData[2].Item1.wheeltype);
                            m_bit15 = true;
                            double regionsize = 0.0;
                            try
                            {

                                m_sqlfun.connection(constr);
                                m_sqlfun.Sql_open();

                                if (m_sqlfun.conn.State == ConnectionState.Open)
                                {
                                    string typename = ConQueue_PassData.ElementAt(2).Item1.wheeltype;
                                    bool exist = m_sqlfun.Sql_ExistColumn("轮毂参数", "轮毂型号", typename);

                                    if (exist)
                                    {
                                        m_sqlfun.Sql_Find("厚度检测开关", "轮毂参数", typename, out regionsize);

                                        m_sqlfun.conn.Close();

                                    }

                                }


                            }
                            catch (Exception ex)
                            {
                                logger.Info(ex.ToString());
                            }
                            finally
                            {
                                m_mainpccomplc.SetInt(m_writedb, 816, (short)Convert.ToInt32(regionsize));//取暂存区数据成功


                            }

                        }
                        if (!m_readplcdata.get_thinknessresult)
                        {
                            m_bit15 = false;
                        }
                        #endregion


                        #region 16发送螺栓孔厚度相关数据
                        if (m_readplcdata.set_boltholedata && (!m_bit16))
                        {
                            logger.Info("#16 [" + ConQueue_PassData[1].Item2.ToString() + ", " + ConQueue_PassData[1].Item1.wheeltype);
                            m_bit16 = true;
                            string wheeltype = "";
                            double holeNum = 0;

                            double regionsize1 = 0.0, regionsize2 = 0.0, regionsize3 = 0.0, regionsize4 = 0.0;
                            try
                            {
                                wheeltype = ConQueue_PassData.ElementAt(1).Item1.wheeltype;
                                logger.Info("轮毂型号:" + wheeltype);

                                m_sqlfun.connection(constr);
                                m_sqlfun.Sql_open();
                                if (m_sqlfun.conn.State == ConnectionState.Open)
                                {
                                    m_sqlfun.Sql_Find("后距空间", "轮毂参数", wheeltype, out regionsize1);
                                    m_sqlfun.Sql_Find("总高", "轮毂参数", wheeltype, out regionsize2);
                                    m_sqlfun.Sql_Find("螺栓孔厚度工位伺服移动", "轮毂参数", wheeltype, out regionsize3);
                                    m_sqlfun.Sql_Find("螺栓孔厚度", "轮毂参数", wheeltype, out regionsize4);
                                    m_sqlfun.Sql_Find("孔数", "轮毂参数", wheeltype, out holeNum);
                                }
                                m_sqlfun.conn.Close();
                            }
                            catch (Exception e)
                            {
                                logger.Error(e.Message);
                            }
                            finally
                            {
                                try
                                {
                                    m_mainpccomplc.SetFloat(m_writedb, 554, (float)regionsize1);
                                    m_mainpccomplc.SetFloat(m_writedb, 558, (float)regionsize2);
                                    m_mainpccomplc.SetFloat(m_writedb, 562, (float)regionsize3);
                                    m_mainpccomplc.SetFloat(m_writedb, 824, (float)regionsize4);

                                    short tmp = (short)Convert.ToInt32(holeNum);
                                    m_mainpccomplc.SetInt(m_writedb, 828, tmp);
                                    logger.Info("hole num " + tmp);

                                    m_mainpccomplc.SetString(m_writedb, 566, wheeltype);
                                }
                                catch (Exception e)
                                {
                                    logger.Error(e.Message + e.StackTrace);
                                }
                                logger.Info("已写入螺栓孔数据");
                                m_mainpccomplc.SetInt(m_writedb, 822, 666);

                            }


                        }
                        if (!m_readplcdata.set_boltholedata)
                        {
                            m_bit16 = false;
                        }
                        #endregion

                    }
                    catch (Exception e)
                    {
                        logger.Error(e.Message + e.StackTrace);
                    }

                }

                m_mainpccomplc.DisConnect();
                logger.Info("plc连接断开");
            }
            else
            {
                logger.Info("plc连接失败");
            }


            s7commun.tB_Status.BackColor = Color.Red;
            s7commun.DisconnectBtn.Enabled = false;
            s7commun.ConnectBtn.Enabled = true;

            if(connection != 0 || m_plcstatus == true)
            {
                // 未连接成功或非正常断开连接，尝试重连
                m_plcstatus = false;

                if (mPLCReonnCount < 3)
                {
                    mPLCReonnCount += 1;
                    new Thread(() =>
                    {
                        Thread.Sleep(1000);
                        GetPlcData(ip, Rack, Slot, readdb, writedb);
                    }).Start();
                }
            }
        }

        private void Btm_ImageDown_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (Directory.Exists(C1saveimagepath) || Directory.Exists(C2saveimagepath) || Directory.Exists(C3saveimagepath)|| Directory.Exists(C4saveimagepath))
            {
                if (m_btnState == false)
                {

                    this.m_btnState = true;
                    Btm_ImageDown.ItemAppearance.Normal.BackColor = Color.Cyan;
                }
                else
                {

                    this.m_btnState = false;
                    Btm_ImageDown.ItemAppearance.Normal.BackColor = Color.Empty;
                }
            }
            else
            {
                
                MessageBoxEX.Show(res.GetString("fileNotExit"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
            }

        }


        private void barButtonItem9_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

        }

        private void Btm_Setting_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            var settingForm = new Setting();
            if(settingForm.ShowDialog() == DialogResult.OK)
            {
                loadConfig();
            }
        }

        private void ribbonControl1_Click(object sender, EventArgs e)
        {

        }


        private void Btm_FilePath_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

            FolderBrowserDialog dialog = new FolderBrowserDialog();
            dialog.Description = res.GetString("chooseContent"); ;//左上角提示


            if (dialog.ShowDialog() == DialogResult.OK)
            {
                switch (cbEnumCamera.Text)
                {
                    case "C1":
                        C1saveimagepath = dialog.SelectedPath;//获取选中C1文件路径
                        break;
                    case "C2":
                        C2saveimagepath = dialog.SelectedPath;//获取选中C2文件路径
                        break;
                    case "C3":
                        C3saveimagepath = dialog.SelectedPath;//获取选中C3文件路径
                        break;
                    case "C4":
                        C4saveimagepath = dialog.SelectedPath;//获取选中C4文件路径
                        break;

                }




            }

        }

        private void btnAbout_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            var dlg = new About();
            dlg.ShowDialog();
        }

        //private void Btm_Run_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        //{

        //    string selectitem = cbEnumCamera.SelectedItem.ToString();
        //    switch (selectitem)
        //    {
        //        case "C2":
        //            if (createstatus2)
        //            {

        //                this.label_Camera1status.ItemAppearance.Normal.BackColor = Color.Green;
        //                this.Btm_Stop.Enabled = true;
        //                this.Btm_Run.Enabled = false;
        //                m_c2btnstatus[2] = false;
        //                m_c2btnstatus[3] = true;
        //                // mCamera1.StartGrabbing();
        //                m_HKCamera1.StartGrab();
        //                //  m_HKCamera2.StartGrab();

        //            }
        //            else
        //                MessageBox.Show("请连接相机C2！");

        //            break;
        //        case "C3":
        //            if (createstatus3)
        //            {

        //                this.label_Camera2status.ItemAppearance.Normal.BackColor = Color.Green;
        //                this.Btm_Stop.Enabled = true;
        //                this.Btm_Run.Enabled = false;
        //                m_c3btnstatus[2] = false;
        //                m_c3btnstatus[3] = true;
        //                // mCamera1.StartGrabbing();
        //                m_HKCamera2.StartGrab();
        //                //  m_HKCamera2.StartGrab();

        //            }
        //            else
        //                MessageBox.Show("请连接相机C3！");

        //            break;
        //        default:
        //            MessageBox.Show("请先连接C2、C3相机");
        //            break;
        //    }



        //}

        //private void Btm_Stop_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        //{
        //    string selectitem = cbEnumCamera.SelectedItem.ToString();
        //    switch (selectitem)
        //    {
        //        case "C2":

        //            if (createstatus2)
        //            {

        //                m_HKCamera1.StopGrap();
        //                //createstatus2 = false;
        //                this.Btm_Stop.Enabled = false;
        //                this.Btm_Run.Enabled = true;
        //                m_c2btnstatus[2] = true;
        //                m_c2btnstatus[3] = false;


        //            }
        //            break;
        //        case "C3":
        //            if (createstatus3)
        //            {

        //                //createstatus3 = false;
        //                m_HKCamera2.StopGrap();
        //                this.Btm_Stop.Enabled = false;
        //                this.Btm_Run.Enabled = true;
        //                m_c3btnstatus[2] = true;
        //                m_c3btnstatus[3] = false;


        //            }
        //            break;
        //    }

        //}



        private void Btm_Record_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //if (savetopcstatus == true)
            //{
            //    savedatatpc = false;
            //    savetopcstatus = false;
            //    Btm_Record.ItemAppearance.Normal.BackColor = Color.Empty;


            //}

            //else
            //{
            //    savedatatpc = true;
            //    savetopcstatus = true;
            //    Btm_Record.ItemAppearance.Normal.BackColor = Color.Cyan;
            //}
            string typename = "", score = "", usetime = "";
            HObject hImg;
            HOperatorSet.GenEmptyObj(out hImg);
            int int_attackers = Environment.ProcessorCount;
            _semaphore = new SemaphoreSlim(int_attackers);
            DirectoryInfo path_string = new DirectoryInfo(Application.StartupPath);
            string filePath = path_string + "\\model\\registerpicture\\02563C.bmp";
            HOperatorSet.ReadImage(out hImg, filePath);
            SearchPattem(hImg, hWindowControlC1, out typename, out score, out usetime);
        }

   

        private void LoadAll(Form form)
        {
            MultiLanguage multiLanguage = new MultiLanguage();
            multiLanguage.LoadLanguage(form, typeof(Form1));
            if (form.Name == "Form1")
            {
                multiLanguage.LoadLanguage(form, typeof(Form1));
            }
            if (form.Name == "Patterm")
            {
                multiLanguage.LoadLanguage(form, typeof(Patterm));
            }
            if (form.Name == "S7communication")
            {
                multiLanguage.LoadLanguage(form, typeof(S7communication));
            }
            if (form.Name == "HKLaser")
            {
                multiLanguage.LoadLanguage(form, typeof(HKLaser));
            }
            if (form.Name == "Technology")
            {
                multiLanguage.LoadLanguage(form, typeof(Technology));
            }
            if (form.Name == "DataBase")
            {
                multiLanguage.LoadLanguage(form, typeof(DataBase));
            }
            if (form.Name == "LogIn")
            {
                multiLanguage.LoadLanguage(form, typeof(LogIn));
            }

        }


        /**//// <summary>
            /// 应用资源
            /// ApplyResources 的第一个参数为要设置的控件
            ///                  第二个参数为在资源文件中的ID，默认为控件的名称
            /// </summary>
        private void ApplyResource()
        {
            System.ComponentModel.ComponentResourceManager res = new ComponentResourceManager(typeof(Form1));
            foreach (Control ctl in Controls)
            {
                res.ApplyResources(ctl, ctl.Name);
            }
            this.ResumeLayout(false);
            this.PerformLayout();
            res.ApplyResources(this, "$this");
        }


        private void zh_barStaticItem_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            MultiLanguage multiLanguage = new MultiLanguage();
            //修改默认语言
            multiLanguage.SetDefaultLanguage("zh");
            //对所有打开的窗口重新加载语言
            FormCollection collection = Application.OpenForms;
            for (int i = 0; i < collection.Count; i++)
            {
                LoadAll(collection[i]);
            }
        }
        private void barStaticItem4_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Type FormType = this.GetType();

            MultiLanguage multiLanguage = new MultiLanguage();
            //修改默认语言
            multiLanguage.SetDefaultLanguage("en");
            //对所有打开的窗口重新加载语言
            FormCollection collection = Application.OpenForms;
            for (int i = 0; i < collection.Count; i++)
            {
                LoadAll(collection[i]);
            }

        }

        private void es_barStaticItem_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            MultiLanguage multiLanguage = new MultiLanguage();
            //修改默认语言
            multiLanguage.SetDefaultLanguage("es");
            //对所有打开的窗口重新加载语言
            FormCollection collection = Application.OpenForms;
            for (int i = 0; i < collection.Count; i++)
            {
                LoadAll(collection[i]);
            }
        }

        private void barButtonItem3_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Type FormType = this.GetType();

            MultiLanguage multiLanguage = new MultiLanguage();
            //修改默认语言
            multiLanguage.SetDefaultLanguage("fr");
            //对所有打开的窗口重新加载语言
            FormCollection collection = Application.OpenForms;
            for (int i = 0; i < collection.Count; i++)
            {
                LoadAll(collection[i]);
            }
        }


        private void cbEnumCamera_SelectedIndexChanged_1(object sender, EventArgs e)
        {
            string selectitem = cbEnumCamera.SelectedItem.ToString();

            switch (selectitem)
            {
                case "C1":
                    Btm_Connect.Enabled = m_c1btnstatus[0];
                    Btm_DisConnect.Enabled = m_c1btnstatus[1];


                    break;
                case "C2":
                    Btm_Connect.Enabled = m_c2btnstatus[0];
                    Btm_DisConnect.Enabled = m_c2btnstatus[1];


                    break;
                case "C3":
                    Btm_Connect.Enabled = m_c3btnstatus[0];
                    Btm_DisConnect.Enabled = m_c3btnstatus[1];

                    break;
                case "C4":
                    Btm_Connect.Enabled = m_c4btnstatus[0];
                    Btm_DisConnect.Enabled = m_c4btnstatus[1];

                    break;

            }
        }

       

        private void btn_3dfun_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            HKLaser m_hklaser = new HKLaser();
            m_hklaser.evenGetDatabasename += Getdatabasename;
            m_hklaser.Show();
        }

        private void Btm_ConnectDatabase_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            SQlFun m_sqlfun = new SQlFun();

            m_sqlfun.connection(constr);
            if (m_btndatabaseState == false)
            {

                if (m_sqlfun.Sql_open())
                {
                    this.label_DBStatus.ItemAppearance.Normal.BackColor = Color.Cyan;
                    this.Btm_ConnectDatabase.ItemAppearance.Normal.BackColor = Color.Cyan;
                    Btm_ConnectDatabase.Caption = res.GetString("database_discon");
                    this.m_btndatabaseState = true;
                    m_databaseconnect = true;
                }

                else
                    this.label_DBStatus.ItemAppearance.Normal.BackColor = Color.Red;


            }
            else
            {
                m_sqlfun.conn.Close();

                this.label_DBStatus.ItemAppearance.Normal.BackColor = Color.Red;
                this.Btm_ConnectDatabase.ItemAppearance.Normal.BackColor = Color.Empty;
                this.Btm_ConnectDatabase.Caption = res.GetString("database_con");
                this.m_btndatabaseState = false;
                m_databaseconnect = false;
            }


        }

        private void Btm_Log_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {


        }

        private double GetWheelParam(string wheeltype, string field)
        {
            try
            {
                if (m_dbcache.ContainsKey(wheeltype))
                {
                    if(m_dbcache[wheeltype].ContainsKey(field))
                    {
                        return m_dbcache[wheeltype][field];
                    }
                    else
                    {
                        logger.Warn("field [" + field + "] not found");
                    }
                }
                else
                {
                    logger.Warn("wheel [" + field + "] not found");
                }
            }
            catch(Exception e)
            {
                logger.Warn(e.Message + e.StackTrace);
            }
            return 0;
        }
        
        private bool HasWheel(string wheeltype)
        {
            if(string.IsNullOrEmpty(wheeltype))
            {
                return false;
            }
            return m_dbcache.ContainsKey(wheeltype);
        }

        void BuildDatabaseCache()
        {
            try
            {
                m_dbcache.Clear();
                logger.Info(constr);
                var conn = new SqlConnection(constr);
                conn.Open();
                if (conn.State == ConnectionState.Open)
                {
                    string sql = @"USE MyDB SELECT * FROM 轮毂参数 order by 序号 desc";
                    var cmd = new SqlCommand(sql, conn);
                    var reader = cmd.ExecuteReader();
                    logger.Info("FieldCount " + reader.FieldCount.ToString());
                    while (reader.Read())
                    {
                        string wheeltype = "";
                        var row = new Dictionary<string, double>();
                        for (int i = 0; i < reader.FieldCount; ++i)
                        {
                            string fieldName = reader.GetName(i);
                            if(fieldName.Equals("轮毂型号"))
                            {
                                if (reader.IsDBNull(i))
                                {
                                    break;
                                }
                                wheeltype = reader.GetString(i);
                            }
                            else
                            {
                                if (reader.IsDBNull(i))
                                {
                                    row.Add(fieldName, 0);
                                    continue;
                                }
                                try
                                {
                                    double value = double.Parse(reader.GetValue(i).ToString());
                                    row.Add(fieldName, value);
                                }
                                catch (Exception e)
                                {
                                    logger.Error(e.Message);
                                }
                            }
                        }
                        if (!wheeltype.Equals("") && !m_dbcache.ContainsKey(wheeltype))
                        {
                            m_dbcache.Add(wheeltype, row);
                        }
                    }
                    logger.Info("dbcache build finish");
                }
                else
                {
                    logger.Error("数据库连接失败");
                }
            }
            catch(Exception e)
            {
                logger.Error(e.Message + e.StackTrace);
            }
        }


        private void Btm_LoadRecipe_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Technology t_technology = new Technology();
            t_technology.wheelParam = m_wheelParam;
            t_technology.evenGetDatabasename += Getdatabasename;
            t_technology.OnImported += () => {
                if (m_feature)
                {
                    BuildDatabaseCache();
                }
            };
            t_technology.Show();
            
        }
        private void Getdatabasename(ref string databasename,ref string numberofdecima)
        {
            databasename = Edit_Databasename.EditValue.ToString();
            numberofdecima= m_decimalnum.ToString();
           
        }

        private bool cameraparasetFirstShow = true;
        private void Btm_SetPara_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            m_cameraparaset1.Show();
            m_cameraparaset1.groupBox3.Enabled = createstatus1;
            m_cameraparaset1.groupBox6.Enabled = createstatus2;
            m_cameraparaset1.groupBox9.Enabled = createstatus3;
            m_cameraparaset1.groupBox12.Enabled = createstatus4;

            if (cameraparasetFirstShow)
            {
                cameraparasetFirstShow = false;

                m_cameraparaset1.eventdelegateSetExternTrigger1 += SetExternTrigger1;
                m_cameraparaset1.eventdelegateSetSoftTrigger1 += SetSoftTrigger1;
                m_cameraparaset1.eventdelegateSetFreeRun1 += Freerun1;
                m_cameraparaset1.eventdelegateSetTrigger1 += Trigger1;
                m_cameraparaset1.eventdelegateSetExposure1_Scroll1 += Exposure1;
                m_cameraparaset1.eventdelegateSetGain1_Scroll1 += Gain1;
                m_cameraparaset1.eventdelegateSoftwareExecute1 += SoftwareExecute1;
                
                m_cameraparaset1.eventdelegateSetExternTrigger2 += SetExternTrigger2;
                m_cameraparaset1.eventdelegateSetSoftTrigger2 += SetSoftTrigger2;
                m_cameraparaset1.eventdelegateSetFreeRun2 += Freerun2;
                m_cameraparaset1.eventdelegateSetTrigger2 += Trigger2;
                m_cameraparaset1.eventdelegateSetExposure1_Scroll2 += Exposure2;
                m_cameraparaset1.eventdelegateSetGain1_Scroll2 += Gain2;
                m_cameraparaset1.eventdelegateSoftwareExecute2 += SoftwareExecute2;
                
                m_cameraparaset1.eventdelegateSetExternTrigger3 += SetExternTrigger3;
                m_cameraparaset1.eventdelegateSetSoftTrigger3 += SetSoftTrigger3;
                m_cameraparaset1.eventdelegateSetFreeRun3 += Freerun3;
                m_cameraparaset1.eventdelegateSetTrigger3 += Trigger3;
                m_cameraparaset1.eventdelegateSetExposure1_Scroll3 += Exposure3;
                m_cameraparaset1.eventdelegateSetGain1_Scroll3 += Gain3;
                m_cameraparaset1.eventdelegateSoftwareExecute3 += SoftwareExecute3;

                m_cameraparaset1.eventdelegateSoftwareExecute4 += SoftwareExecute4;
            }


        }

        private void Btm_Register_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            FormRegister frmRegister = new FormRegister();
            frmRegister.ShowDialog();
        }













        //PLC连续离线测试

        //            private void PlcTest()
        //        {



        //            TotalCount = 0;
        //            HTuple ho_height = null, ho_width = null;
        //            int i = 0;

        //                while(true)
        //                {

        ////                    if (trigger.Count!=0)
        ////{

        //    if(trigger == true)
        //    {
        //        textBox_message.Text = "获得PLC消息";

        //        HTuple hubsize = null, centerRow = null, centerColumn = null, Angle = null;
        //        HTuple deg_angle = null;
        //        short direction = 0;//角度方向

        //        double[] angle = { 0.0, 0.0, 0.0, 0.0, 0.0 };


        //        i++;
        //        if (i < dirInfo.Count())
        //        {

        //            HOperatorSet.ReadImage(out ho_Image, dirInfo[i].FullName);
        //            HOperatorSet.GetImageSize(ho_Image, out ho_width, out ho_height);
        //            HOperatorSet.SetPart(hWindowControl1.HalconWindow, 0, 0, ho_height, ho_width);
        //            HOperatorSet.DispObj(ho_Image, hWindowControl1.HalconWindow);
        //            TotalCount++;
        //            //lblCount1.Text = TotalCount.ToString();




        //            string typename = "";
        //            int int_attackers = Environment.ProcessorCount;


        //            _semaphore = new SemaphoreSlim(int_attackers - 1);
        //            try
        //            {

        //                Screeninghubsize(ho_Image, hWindowControl1, out hubsize, out centerRow, out centerColumn);

        //                wheelsize whlsz = Pixelconversion(hubsize);
        //                if (whlsz != wheelsize.outrang)
        //                {
        //                    SearchPattem(ho_Image, hWindowControl1, whlsz, out typename);
        //                    Task.WaitAll();



        //                    if ((typename != ""))
        //                    {
        //                        Findsmallcircle(typename, hubsize, centerRow, centerColumn, hWindowControl1, out Angle);
        //                        HOperatorSet.TupleDeg(Angle, out deg_angle);
        //                        angle = deg_angle.ToDArr();
        //                    }
        //                }
        //                else
        //                {
        //                    textBox_message.Text = "不在使用范围！";
        //                    if (m_sqlfun.conn.State == ConnectionState.Open)
        //                    {
        //                        databasemaxindex = m_sqlfun.Sql_indexmax("轮型识别1");
        //                        databasemaxindex++;
        //                                    m_sqlfun.Sql_write(databasemaxindex, "12333","", textBox_message.Text);
        //                    }
        //                }



        //            }
        //            catch (HalconException ex)
        //            {

        //                textBox_message.Text = ex.ToString().Substring(0, 100);

        //                if (m_sqlfun.conn.State == ConnectionState.Open)
        //                {
        //                    databasemaxindex = m_sqlfun.Sql_indexmax("轮型识别1");
        //                    databasemaxindex++;
        //                   m_sqlfun.Sql_write(databasemaxindex, "12333","", ex.ToString().Substring(0, 100));
        //                }

        //                return;

        //            }
        //            finally
        //            {

        //                _semaphore.Dispose();
        //                ho_Image.Dispose();
        //                if (angle[0] >= 0)
        //                    direction = 2;
        //                else
        //                    direction =1;

        //                //通讯部分
        //                if (s7commun.connectresult == 0)
        //                {
        //                    short k = 0; k++;
        //                    sendDatainter.sendbitdata[1] = m_softrunstatus;//相机连接状态
        //                    sendDatainter.sendbitdata[2] = false;
        //                    sendDatainter.sendbitdata[3] = false;
        //                    sendDatainter.sendintdata[0] = 300;//轮毂型号
        //                    sendDatainter.sendintdata[1] = k;//

        //                    sendDatainter.sendintdata[2] = direction;//选择方向
        //                    sendDatainter.sendfloatdata[0] = (float)angle[0];// 角度
        //                    sendDatainter.sendstringdata[0] = typename;
        //                    s7commun.send(sendDatainter);


        //                }
        //                HOperatorSet.WaitSeconds(1);
        //                //通讯部分




        //            }
        //    }

        //    }
        //    else
        //        textBox_message.Text = "未获得PLC消息";





        ////}

        //                }



        //        }



        private void MDCListB_SelWheelType(object sender, MouseEventArgs e)
        {
            if (e.Button == System.Windows.Forms.MouseButtons.Right)
            {

                CListB_SelRightButtom m_clistdel = new CListB_SelRightButtom();
                CListBAdd m_clistadd = new CListBAdd(); ;//列表中添加新型号
                m_clistdel.evenDelCListB += CheckListBoxDel;
                m_clistadd.eventAddCListB += CheckListBoxAdd;
                m_clistdel.t_CListBAdd = m_clistadd;
                m_clistdel.StartPosition = FormStartPosition.Manual;
                m_clistdel.Show();
            }
        }

        private void Btn_Mitsubishi_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

            
        }

        private void barButtonItem6_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            var dlg = new PdfViewer();
            dlg.ShowDialog();
            return;

           // //((DevExpress.XtraEditors.Repository.RepositoryItemComboBox)控件名.Edit).Items.Add(item);
           // try
           // {
           //     double outdata;
           //     setnumberofdecimalpoint(10.753486, m_decimalnum, out outdata);
           //     MessageBox.Show(outdata.ToString());
           // }
           //catch(Exception ex)
           // {
           //     MessageBox.Show(ex.ToString());
           // }
          

            

           // //string str ="1 "+ ConQueue_PassData[4].wheeltype + "\r\n";
           // //str += "2 "+ ConQueue_PassData[3].wheeltype + "\r\n";
           // //str += "3 "+ ConQueue_PassData[2].wheeltype + "\r\n";
           // //str += "4 "+ ConQueue_PassData[1].wheeltype + "\r\n";
           // //str += "5 "+ ConQueue_PassData[0].wheeltype + "\r\n";
           // //MessageBox.Show(str);


        }



        //相机连接
        private void Btm_Connect_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //此部分为巴斯勒相机的
            //if (mCamera1.OpenCam() == 0)
            //{
            //    mCamera1.SetHeartBeatTime(5000);
            //    mCamera1.eventProcessImage += processHImage1;
            //    mCamera1.eventComputeGrabTime += GrabTime1;
            //    mCamera1.GetMinMaxExposureTime();
            //    mCamera1.GetMinMaxGain();
            //    mCamera1.numWindowIndex = 0;
            //    createstatus = true;
            //    textBox_message.Text = "相机连接成功！";
            //   this.Btm_DisConnect.Enabled = true;

            //    //saveFileDialog1.Filter = "PNG(*.png)|*.png|BMP (*.bmp)|*.bmp|TIFF (*.tiff)|*.tiff|JPEG(*.jpeg)|*.jpeg";
            //}
            //else
            //    MessageBox.Show("打开相机失败！");

            string selectitem = cbEnumCamera.Text;
            switch (selectitem)
            {
                case "C1":
                    int nRet1 = m_HKCamera1.Open_Camera("C1", m_DeviceList);
                    if (nRet1 != -1)
                    {
                        m_HKCamera1.SetTriggerMode();
                        m_HKCamera1.SetSoftTrigger();

                        m_HKCamera1.eventProcessImage += processHImage1;
                        m_HKCamera1.StartGrab();
                        createstatus1 = true;
                        this.label_Camera1status.ItemAppearance.Normal.BackColor = Color.Green;
                        m_cameraparaset1.groupBox3.Enabled = true;
                        Btm_DisConnect.Enabled = true;
                        Btm_Connect.Enabled = false;

                        m_c1btnstatus[0] = false;
                        m_c1btnstatus[1] = true;
                        logger.Info(res.GetString("C1_connected"));
                        //MessageBoxEX.Show(res.GetString("C1_disconnected"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                    }
                    else
                    {
                        logger.Info(res.GetString("C1_disconnected"));
                        MessageBoxEX.Show(res.GetString("C1_disconnected"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                    }
                    break;



                case "C2":
                    if (m_cameralink)
                    {
                        int nRet2 = m_HKCameraLink1.Open_Camera("C2", m_CameraLinkDeviceList);
                        if (nRet2 != -1)
                        {
                            m_HKCameraLink1.SetTriggerMode();
                            m_HKCameraLink1.SetSoftTrigger();

                            m_HKCameraLink1.eventProcessImage += processHImage2;
                            m_HKCameraLink1.StartGrab();
                            createstatus2 = true;
                            this.label_Camera2status.ItemAppearance.Normal.BackColor = Color.Green;
                            m_cameraparaset1.groupBox6.Enabled = true;
                            Btm_DisConnect.Enabled = true;
                            Btm_Connect.Enabled = false;

                            m_c2btnstatus[0] = false;
                            m_c2btnstatus[1] = true;
                            textBox_message.Text = res.GetString("C2_connected");

                        }
                        else
                        {
                            logger.Info(res.GetString("C2_disconnected"));
                            MessageBoxEX.Show(res.GetString("C2_disconnected"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                        }
                    }
                    else
                    {
                        int nRet2 = m_HKCamera2.Open_Camera("C2", m_DeviceList);
                        if (nRet2 != -1)
                        {
                            m_HKCamera2.SetTriggerMode();
                            m_HKCamera2.SetSoftTrigger();

                            m_HKCamera2.eventProcessImage += processHImage2;
                            m_HKCamera2.StartGrab();
                            createstatus2 = true;
                            this.label_Camera2status.ItemAppearance.Normal.BackColor = Color.Green;
                            m_cameraparaset1.groupBox6.Enabled = true;
                            Btm_DisConnect.Enabled = true;
                            Btm_Connect.Enabled = false;

                            m_c2btnstatus[0] = false;
                            m_c2btnstatus[1] = true;
                            logger.Info(res.GetString("C2_connected"));

                        }
                        else
                        {
                            logger.Info(res.GetString("C2_disconnected"));
                            MessageBoxEX.Show(res.GetString("C2_disconnected"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                        }
                    }

                    break;
                case "C3":
                    int nRet3 = m_HKCamera3.Open_Camera("C3", m_DeviceList);

                    if (nRet3 != -1)
                    {

                        m_HKCamera3.SetTriggerMode();
                        m_HKCamera3.SetSoftTrigger();
                        m_HKCamera3.eventProcessImage += processHImage3;
                        m_HKCamera3.StartGrab();
                        createstatus3 = true;
                        this.label_Camera3status.ItemAppearance.Normal.BackColor = Color.Green;
                        m_cameraparaset1.groupBox9.Enabled = true;
                        Btm_DisConnect.Enabled = true;
                        Btm_Connect.Enabled = false;

                        m_c3btnstatus[0] = false;
                        m_c3btnstatus[1] = true;
                        Btm_DisConnect.Enabled = true;
                        logger.Info(res.GetString("C3_connected"));
                        MessageBoxEX.Show(res.GetString("C3_open"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                    }
                    else
                    {
                        logger.Info(res.GetString("C3_disconnected"));
                        MessageBoxEX.Show(res.GetString("C3_disconnected"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                    }
                    break;
              
                case "C4":
                    int nRet4 = m_HKVision_3D1.Open_Camera("C4", m_DeviceList_3D);

                    if (nRet4 != -1)
                    {

                        //  m_HKVision_3D1.SetTriggerMode();
                        //  m_HKVision_3D1.SetSoftTrigger();
                        m_HKVision_3D1.eventProcessImage += processHImage4;
                        int index = m_DeviceList_3D.IndexOf("C4");
                        m_HKVision_3D1.StartGrab(index);
                        createstatus3 = true;
                        this.label_Camera4status.ItemAppearance.Normal.BackColor = Color.Green;
                        m_cameraparaset1.groupBox12.Enabled = true;
                        Btm_DisConnect.Enabled = true;
                        Btm_Connect.Enabled = false;

                        m_c4btnstatus[0] = false;
                        m_c4btnstatus[1] = true;
                        Btm_DisConnect.Enabled = true;
                        logger.Info(res.GetString("C4_connected"));
                        MessageBoxEX.Show(res.GetString("C4_open"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                    }
                    else
                    {
                        logger.Info(res.GetString("C4_disconnected"));
                        MessageBoxEX.Show(res.GetString("C4_disconnected"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                    }
                    break;
                default:
                    MessageBoxEX.Show(res.GetString("noCamera"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                    break;

            }





        }

        private void barButtonItem7_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            // m_HKVision_3D1.SoftTrigger();
            double a =6365.44-5111.018;
            double b =1545.478-5937.355;
            double sc =125/ Math.Sqrt(a*a+b*b);

        }



        //相机断开连接
        private void Btm_DisConnect_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            string selectitem = cbEnumCamera.Text;
            switch (selectitem)
            {
                case "C1":
                    m_HKCamera1.StopGrap();
                    m_HKCamera1.Close_Camera();
                    m_cameraparaset1.groupBox3.Enabled = false;
                    Btm_DisConnect.Enabled = false;
                    Btm_Connect.Enabled = true;

                    createstatus1 = false;
                    m_c1btnstatus[0] = true;
                    m_c1btnstatus[1] = false;
                    m_c1btnstatus[3] = false;
                    this.label_Camera1status.ItemAppearance.Normal.BackColor = Color.Red;
                    break;

                case "C2":
                    if (m_cameralink)
                    {
                        m_HKCameraLink1.StopGrab();
                        m_HKCameraLink1.Close_Camera();
                    }
                    else
                    {
                        m_HKCamera2.StopGrap();
                        m_HKCamera2.Close_Camera();
                    }
                    m_cameraparaset1.groupBox6.Enabled = false;
                    Btm_DisConnect.Enabled = false;
                    Btm_Connect.Enabled = true;

                    createstatus2 = false;
                    m_c2btnstatus[0] = true;
                    m_c2btnstatus[1] = false;
                    m_c2btnstatus[3] = false;
                    this.label_Camera2status.ItemAppearance.Normal.BackColor = Color.Red;
                    break;
                case "C3":
                    m_HKCamera3.StopGrap();
                    m_HKCamera3.Close_Camera();
                    m_cameraparaset1.groupBox9.Enabled = false;
                    // mCamera1.CloseCam();
                    Btm_DisConnect.Enabled = false;
                    Btm_Connect.Enabled = true;

                    createstatus3 = false;
                    m_c3btnstatus[0] = true;
                    m_c3btnstatus[1] = false;
                    m_c3btnstatus[3] = false;
                    this.label_Camera3status.ItemAppearance.Normal.BackColor = Color.Red;
                    break;
                case "C4":
                  
                    m_HKVision_3D1.StopGrab();
                    m_HKVision_3D1.Close_Camera();
                    m_cameraparaset1.groupBox9.Enabled = false;
                    // mCamera1.CloseCam();
                    Btm_DisConnect.Enabled = false;
                    Btm_Connect.Enabled = true;

                    createstatus4 = false;
                    m_c4btnstatus[0] = true;
                    m_c4btnstatus[1] = false;
                    m_c4btnstatus[3] = false;
                    this.label_Camera4status.ItemAppearance.Normal.BackColor = Color.Red;
                    break;
            }



        }



        private void Btm_LoadDocument_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            FolderBrowserDialog dialog = new FolderBrowserDialog();
            dialog.Description = res.GetString("chooseContent");//左上角提示
            filepath = string.Empty;

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                filepath = dialog.SelectedPath;//获取选中文件路径

            }
            if (filepath != "")
                ;
            //  Btm_TestStart.Enabled = true;

        }

        
        private void Btm_refesh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs ea)
        {
            CheckSelectInChecklistbox();
        }

        private void Btm_FindTemplate_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Patterm m_pattem = new Patterm();
            m_pattem.evenGetDatabasename += Getdatabasename;
            m_pattem.Show();
        }

        private void btn_circlecaliper_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            CircleCaliper m_circlecaliper = new CircleCaliper();
            m_circlecaliper.evenGetDatabasename += Getdatabasename;
            m_circlecaliper.Show();

        }

        private void cbEnumCamera_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selectitem = cbEnumCamera.Text;

            switch (selectitem)
            {
                case "C1":
                    Btm_Connect.Enabled = m_c1btnstatus[0];
                    Btm_DisConnect.Enabled = m_c1btnstatus[1];


                    break;
                case "C2":
                    Btm_Connect.Enabled = m_c2btnstatus[0];
                    Btm_DisConnect.Enabled = m_c2btnstatus[1];


                    break;
                case "C3":
                    Btm_Connect.Enabled = m_c3btnstatus[0];
                    Btm_DisConnect.Enabled = m_c3btnstatus[1];

                    break;
                case "C4":
                    Btm_Connect.Enabled = m_c4btnstatus[0];
                    Btm_DisConnect.Enabled = m_c4btnstatus[1];

                    break;

            }
        }



        private void barButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            // m_HKCamera1.DeviceListAcq(cbEnumCamera);
        }

        private void Btm_TestStart_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            

        }

        private void Btm_TestStop_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

            if (runtaskstatus)
            {
                runtask.Suspend();
                runtaskstatus = false;
                // Btm_TestStop.Caption = "测试继续";
            }
            else
            {
                runtask.Resume();
                runtaskstatus = true;
                // Btm_TestStop.Caption = "测试暂停";
            }

        }
        //相机取图时间函数
        private void GrabTime1(long time)

        {
            //++count1;
            // lblCount1.Text = "[  Count1 : " + count1 + "  ]";
            // toolStripTime1.Text = "Time: "+Convert.ToString(time)+"ms";
            //toolStripTime1.Text = "Time: " + Convert.ToString(count1) + "ms";
        }
        //检测相机连接状态




        private void Btm_DataReport_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            DataBase database = new DataBase();
            database.m_database = m_queryDb;
            database.evenGetDatabasename += Getdatabasename;
            database.Show();
        }
        //给CheckListBox增加型号
        private void CheckListBoxAdd(string wheeltype)
        {
            for (int i = 0; i < CListB_SelWheelType.Items.Count; i++)
            {
                if (CListB_SelWheelType.Items[i].ToString() == wheeltype)
                {
                    MessageBoxEX.Show(res.GetString("alreadyExit"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                    return;

                }


            }

            CListB_SelWheelType.Items.Add(wheeltype);
        }
        //给CheckListBox删除型号
        private void CheckListBoxDel()
        {
            CListB_SelWheelType.Items.RemoveAt(CListB_SelWheelType.SelectedIndex);

        }
        //用户选择轮毂的涂装颜色

        private void UserSelectColor(string color)
        {
            if (m_selectcolor.ContainsKey(m_typenamerecord[1]))
            {
                m_selectcolor.Remove(m_typenamerecord[1]);
            }

            m_selectcolor.Add(m_typenamerecord[1], color);
            m_wheelcolor = m_selectcolor[m_typenamerecord[1]];



        }

        //字典添加轮毂型号映射
        private void DictionaryAdd()
        {
            NPOI t_npoi = new NPOI();
            DataSet dataSet = new System.Data.DataSet();
            try
            {

                dataSet.Tables.Add(t_npoi.ExcelToTable(Application.StartupPath.Trim() + "\\config\\" + "轮毂型号映射表.xls"));
                if (dataSet.Tables.Count != 0)
                {
                    for (int i = 0; i < dataSet.Tables[0].Rows.Count; i++)
                    {
                        if (dataSet.Tables[0].Columns.Count >= 2)
                            m_dictionary.Add(dataSet.Tables[0].Rows[i].ItemArray[0].ToString(), dataSet.Tables[0].Rows[i].ItemArray[1].ToString());

                    }
                }
                else
                {
                    MessageBoxEX.Show(res.GetString("lackTable"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                    return;
                }
            }
            catch
            {
                MessageBoxEX.Show(res.GetString("fileOpened"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });

            }

        }
        //选择的需要着色的进入队列
        private void CheckSelectInChecklistbox()
        {

            m_selectCheckListbox.Clear();

            for (int i = 0; i < CListB_SelWheelType.Items.Count; i++)
            {
                if (CListB_SelWheelType.GetItemChecked(i))
                    m_selectCheckListbox.Add(CListB_SelWheelType.Items[i].ToString());
            }


        }
        //着色选择
        private void IsSelcecColor(string typename)
        {
            m_typenamerecord[0] = m_typenamerecord[1];
            m_typenamerecord[1] = typename;
            if (m_typenamerecord[0] != m_typenamerecord[1])
            {
                m_wheelcolor = res.GetString("noColor");
                foreach (string item in m_selectCheckListbox)
                {
                    if (typename == item)
                    {
                        ColorSelect t_colorselect = new ColorSelect();
                        t_colorselect.evenSelectColor += UserSelectColor;
                        t_colorselect.ShowDialog();


                    }
                }
            }


        }

        //相似轮型的选择
        //private void Select_Similarwheel(string typename,out string lasttypename)
        //{

        //    switch (typename)
        //        {
        //        case "":
        //            break;
        //        case "1":
        //            break;
        //        default:
        //            lasttypename = typename;

        //    }
        //    m_typenamerecord[0] = m_typenamerecord[1];
        //    m_typenamerecord[1] = typename;
        //    if (m_typenamerecord[0] != m_typenamerecord[1])
        //    {
        //        m_wheelcolor = "无";
        //        foreach (string item in m_selectCheckListbox)
        //        {
        //            if (typename == item)
        //            {
        //                ColorSelect t_colorselect = new ColorSelect();
        //                t_colorselect.evenSelectColor += UserSelectColor;
        //                t_colorselect.ShowDialog();


        //            }
        //        }
        //    }


        //}





    }


    //数据传递用结构体
    //public class PassData
    //{
    //    //配方参数
    //    public string wheeltype;
    //    public double lasermov_val, cenholmov_val, hatholmov_val, markmov_val;
    //    public double deep_stdval, deeptolp_stdval, deeptoln_stdval, cenhol_stdval, cenholtolp_stdval, cenholtoln_stdval, hathol_stdval, hatholtolp_stdval, hatholtoln_stdval;
    //    public int hole_num;
    //    public double PCD_stdval, position_stdval;

    //    //检测结果
    //    public double[] laserdeep_val=new double[2];//激光深度检测结果
    //    public double[] position_val=new double[6];//位置度检测结果
    //    public double[] boltdiameter_val = new double[6];
    //    public double cen_val, hat_val;//中心孔、帽止口检测结果

    //    public double lasercompareval, cencompareval, hatcompareval;//检测值与理论值差值
    //    public double[] boltdiametercompareval = new double[6];//直径检测值与理论值差值
    //    public double[] positioncompareval=new double[6];//位置度检测值与理论值差值

    //    public string deep_result, cenhol_result, hathol_result, result;
    //    public string[] boltdiameter_result = new string[6];//直径检测结果集合
    //    public string[]pos_result=new string[6] ;//位置度检测结果集合

    //    public string last_result;//最后结果
    //}
}
