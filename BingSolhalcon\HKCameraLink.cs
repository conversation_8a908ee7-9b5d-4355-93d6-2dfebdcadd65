﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using MvCamCtrl.NET;
using System.Runtime.InteropServices;
using System.Threading;
using System.IO;
using System.Resources;
using BingSolhalcon.resources;

using System.Drawing.Imaging;
using System.Diagnostics;
using System.Collections.ObjectModel;
using HalconDotNet;

namespace BingSolhalcon
{
    class HKCameraLink
    {
        private MyCamera m_pMyCamera=new MyCamera();
        static MyCamera.MV_GENTL_DEV_INFO_LIST m_stDeviceList = new MyCamera.MV_GENTL_DEV_INFO_LIST();
       static MyCamera.MV_GENTL_IF_INFO_LIST m_stIFInfoList = new MyCamera.MV_GENTL_IF_INFO_LIST();
        private MyCamera m_MyCamera = new MyCamera();
        bool m_bGrabbing = false;
        Thread m_hReceiveThread = null;
        public static ResourceManager res = new ResourceManager(typeof(nonUIresx)); //自定义资源字段
        /// 图像处理自定义委托
        /// </summary>
        /// <param name="hImage">halcon图像变量</param>
        public delegate void delegateProcessHImage(HObject hImage);



        /// <summary>
        /// 图像处理委托事件
        /// </summary>
        public event delegateProcessHImage eventProcessImage;

        //public HKCameraLink()
        //{
        //    try
        //    {
        //        m_pMyCamera = new MyCamera();
        //        m_pDeviceList = new MyCamera.MV_CC_DEVICE_INFO_LIST();

        //        // DeviceListAcq(cbEnumCamera);

        //        //  cbDeviceList = new ComboBox();
        //    }
        //    catch (Exception e)
        //    {
        //        MessageBoxEX.Show(e.ToString(), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
        //    }
        //}
        //枚举相机
        static  public void EnumInterface(string ctifilepath,ref List<string> InterfaceList)
        {

            System.GC.Collect();
            // cmbDeviceList.Items.Clear();
            // cmbInterfaceList.Items.Clear();
            //  cmbDeviceList.Text = "";
            //  cmbInterfaceList.Text = "";
            //OpenFileDialog FileDialog = new OpenFileDialog();
            //if (null == FileDialog)
            //{
            // //   ShowErrorMsg("Open File Dialog Fail!", MyCamera.MV_E_RESOURCE);
            //    return;
            //}

            ////获取选择的DCF文件路径
            //FileDialog.Filter = "DCF文件(*.cti)|*.cti";
            //FileDialog.ShowDialog();

            int nRet = MyCamera.MV_CC_EnumInterfacesByGenTL_NET(ref m_stIFInfoList, ctifilepath);
            if (0 != nRet)
            {
            //    ShowErrorMsg("Enumerate interfaces fail!", nRet);
                return;
            }
            for (UInt32 i = 0; i < m_stIFInfoList.nInterfaceNum; i++)
            {
                MyCamera.MV_GENTL_IF_INFO stIFInfo = (MyCamera.MV_GENTL_IF_INFO)Marshal.PtrToStructure(m_stIFInfoList.pIFInfo[i], typeof(MyCamera.MV_GENTL_IF_INFO));

                InterfaceList.Add("TLType:" + stIFInfo.chTLType + " " + stIFInfo.chInterfaceID + " " + stIFInfo.chDisplayName);
            }
           

        }
        static public void DeviceListAcq(ref List<string> DeviceList,int index )
        {
            // ch:创建设备列表 | en:Create Device List
            System.GC.Collect();

            
            MyCamera.MV_GENTL_IF_INFO stIFInfo = (MyCamera.MV_GENTL_IF_INFO)Marshal.PtrToStructure(m_stIFInfoList.pIFInfo[index],
                typeof(MyCamera.MV_GENTL_IF_INFO));

            int nRet = MyCamera.MV_CC_EnumDevicesByGenTL_NET(ref stIFInfo, ref m_stDeviceList);
            if (0 != nRet)
            {
                //ShowErrorMsg("Enumerate devices fail!", 0);
                return;
            }

            // ch:在窗体列表中显示设备名 | en:Display device name in the form list
            for (int i = 0; i < m_stDeviceList.nDeviceNum; i++)
            {
                MyCamera.MV_GENTL_DEV_INFO device = (MyCamera.MV_GENTL_DEV_INFO)Marshal.PtrToStructure(m_stDeviceList.pDeviceInfo[i], typeof(MyCamera.MV_GENTL_DEV_INFO));

                if (device.chUserDefinedName != "")
                {
                    DeviceList.Add(device.chUserDefinedName);
                }
                
            }

          
        }
        //打开相机
        public int Open_Camera(string cameraname, List<string> listcamera)
        {
            if (m_stDeviceList.nDeviceNum == 0)
            {
                MessageBoxEX.Show(res.GetString("noDevice"), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                return -1;
            }
            int nRet = -1;

            //if (m_stDeviceList.nDeviceNum == 0 || cmbDeviceList.SelectedIndex == -1)
            //{
            //    ShowErrorMsg("No device, please select", 0);
            //    return;
            //}
            int index = listcamera.IndexOf(cameraname);
            // ch:获取选择的设备信息 | en:Get selected device information


            if (index != -1)
            {


                MyCamera.MV_GENTL_DEV_INFO device = (MyCamera.MV_GENTL_DEV_INFO)Marshal.PtrToStructure(m_stDeviceList.pDeviceInfo[index], typeof(MyCamera.MV_GENTL_DEV_INFO));


                // ch:打开设备 | en:Open device
                if (null == m_MyCamera)
                {
                    //  m_MyCamera = new MyCamera();
                    if (null == m_MyCamera)
                    {
                        return -1;
                    }
                }

               nRet = m_MyCamera.MV_CC_CreateDeviceByGenTL_NET(ref device);
                if (MyCamera.MV_OK != nRet)
                {
                    return -1;
                }

                nRet = m_MyCamera.MV_CC_OpenDevice_NET();
                if (MyCamera.MV_OK != nRet)
                {
                    m_MyCamera.MV_CC_DestroyDevice_NET();

                    return -1;
                }

                // ch:设置采集连续模式 | en:Set Continues Aquisition Mode
                m_MyCamera.MV_CC_SetEnumValue_NET("AcquisitionMode", (uint)MyCamera.MV_CAM_ACQUISITION_MODE.MV_ACQ_MODE_CONTINUOUS);
                m_MyCamera.MV_CC_SetEnumValue_NET("TriggerMode", (uint)MyCamera.MV_CAM_TRIGGER_MODE.MV_TRIGGER_MODE_OFF);
                return 0;
            }
            else
            {
                return -1;
            }

        }
        //关闭相机
        public void Close_Camera()
        {
            if (m_bGrabbing == true)
            {
                m_bGrabbing = false;
                m_hReceiveThread.Join();
            }
            // ch:关闭设备 | en:Close Device
            m_MyCamera.MV_CC_CloseDevice_NET();
            m_MyCamera.MV_CC_DestroyDevice_NET();
        }


        //设置连续模式
        public void SetContinuesMode()
        {
            m_MyCamera.MV_CC_SetEnumValue_NET("TriggerMode", (uint)MyCamera.MV_CAM_TRIGGER_MODE.MV_TRIGGER_MODE_OFF);
        }
        //设置触发模式
        public void SetTriggerMode()
        {
            m_MyCamera.MV_CC_SetEnumValue_NET("TriggerMode", (uint)MyCamera.MV_CAM_TRIGGER_MODE.MV_TRIGGER_MODE_ON);
        }
        //设置软触发
        public void SetSoftTrigger()
        {
            m_MyCamera.MV_CC_SetEnumValue_NET("TriggerSource", (uint)MyCamera.MV_CAM_TRIGGER_SOURCE.MV_TRIGGER_SOURCE_SOFTWARE);
        }
        //设置硬触发
        public void SetExternTrigger()
        {
            m_MyCamera.MV_CC_SetEnumValue_NET("TriggerSource", (uint)MyCamera.MV_CAM_TRIGGER_SOURCE.MV_TRIGGER_SOURCE_LINE0);
        }

        private bool IsColorPixelFormat(MyCamera.MvGvspPixelType enType)
        {
            switch (enType)
            {
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_RGB8_Packed:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BGR8_Packed:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_RGBA8_Packed:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BGRA8_Packed:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_YUV422_Packed:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_YUV422_YUYV_Packed:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerGR8:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerRG8:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerGB8:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerBG8:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerGB10:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerGB10_Packed:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerBG10:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerBG10_Packed:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerRG10:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerRG10_Packed:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerGR10:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerGR10_Packed:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerGB12:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerGB12_Packed:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerBG12:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerBG12_Packed:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerRG12:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerRG12_Packed:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerGR12:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_BayerGR12_Packed:
                    return true;
                default:
                    return false;
            }
        }
        private bool IsMonoPixelFormat(MyCamera.MvGvspPixelType enType)
        {
            switch (enType)
            {
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_Mono8:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_Mono10:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_Mono10_Packed:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_Mono12:
                case MyCamera.MvGvspPixelType.PixelType_Gvsp_Mono12_Packed:
                    return true;
                default:
                    return false;
            }
        }
        public void ReceiveImageWorkThread(object obj)
        {

            MyCamera.MV_FRAME_OUT stFrameOut = new MyCamera.MV_FRAME_OUT();
            MyCamera.MV_DISPLAY_FRAME_INFO stDisplayInfo = new MyCamera.MV_DISPLAY_FRAME_INFO();
            IntPtr pTemp = IntPtr.Zero;
            IntPtr pImageBuf = IntPtr.Zero;
            int nImageBufSize = 0;
            HObject Hobj = new HObject();
            while (m_bGrabbing)
            {
                int nRet = m_MyCamera.MV_CC_GetImageBuffer_NET(ref stFrameOut, 1000);
                if (nRet == MyCamera.MV_OK)
                {


                    if (IsColorPixelFormat(stFrameOut.stFrameInfo.enPixelType))
                    {
                        if (stFrameOut.stFrameInfo.enPixelType == MyCamera.MvGvspPixelType.PixelType_Gvsp_RGB8_Packed)
                        {
                            pTemp = stFrameOut.pBufAddr;
                        }
                        else
                        {
                            if (IntPtr.Zero == pImageBuf || nImageBufSize < (stFrameOut.stFrameInfo.nWidth * stFrameOut.stFrameInfo.nHeight * 3))
                            {
                                if (pImageBuf != IntPtr.Zero)
                                {
                                    Marshal.FreeHGlobal(pImageBuf);
                                    pImageBuf = IntPtr.Zero;
                                }

                                pImageBuf = Marshal.AllocHGlobal((int)stFrameOut.stFrameInfo.nWidth * stFrameOut.stFrameInfo.nHeight * 3);
                                if (IntPtr.Zero == pImageBuf)
                                {
                                    break;
                                }
                                nImageBufSize = stFrameOut.stFrameInfo.nWidth * stFrameOut.stFrameInfo.nHeight * 3;
                            }

                            MyCamera.MV_PIXEL_CONVERT_PARAM stPixelConvertParam = new MyCamera.MV_PIXEL_CONVERT_PARAM();

                            stPixelConvertParam.pSrcData = stFrameOut.pBufAddr;//源数据
                            stPixelConvertParam.nWidth = stFrameOut.stFrameInfo.nWidth;//图像宽度
                            stPixelConvertParam.nHeight = stFrameOut.stFrameInfo.nHeight;//图像高度
                            stPixelConvertParam.enSrcPixelType = stFrameOut.stFrameInfo.enPixelType;//源数据的格式
                            stPixelConvertParam.nSrcDataLen = stFrameOut.stFrameInfo.nFrameLen;

                            stPixelConvertParam.nDstBufferSize = (uint)nImageBufSize;
                            stPixelConvertParam.pDstBuffer = pImageBuf;//转换后的数据
                            stPixelConvertParam.enDstPixelType = MyCamera.MvGvspPixelType.PixelType_Gvsp_RGB8_Packed;
                            nRet = m_MyCamera.MV_CC_ConvertPixelType_NET(ref stPixelConvertParam);//格式转换
                            if (MyCamera.MV_OK != nRet)
                            {
                                break;
                            }
                            pTemp = pImageBuf;
                        }

                        try
                        {
                            HOperatorSet.GenImageInterleaved(out Hobj, (HTuple)pTemp, (HTuple)"rgb", (HTuple)stFrameOut.stFrameInfo.nWidth, (HTuple)stFrameOut.stFrameInfo.nHeight, -1, "byte", 0, 0, 0, 0, -1, 0);
                        }
                        catch (System.Exception ex)
                        {
                            MessageBoxEX.Show(ex.ToString(), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                            break;
                        }
                    }
                    else if (IsMonoPixelFormat(stFrameOut.stFrameInfo.enPixelType))
                    {
                        if (stFrameOut.stFrameInfo.enPixelType == MyCamera.MvGvspPixelType.PixelType_Gvsp_Mono8)
                        {
                            pTemp = stFrameOut.pBufAddr;
                        }
                        else
                        {
                            if (IntPtr.Zero == pImageBuf || nImageBufSize < (stFrameOut.stFrameInfo.nWidth * stFrameOut.stFrameInfo.nHeight))
                            {
                                if (pImageBuf != IntPtr.Zero)
                                {
                                    Marshal.FreeHGlobal(pImageBuf);
                                    pImageBuf = IntPtr.Zero;
                                }

                                pImageBuf = Marshal.AllocHGlobal((int)stFrameOut.stFrameInfo.nWidth * stFrameOut.stFrameInfo.nHeight);
                                if (IntPtr.Zero == pImageBuf)
                                {
                                    break;
                                }
                                nImageBufSize = stFrameOut.stFrameInfo.nWidth * stFrameOut.stFrameInfo.nHeight;
                            }

                            MyCamera.MV_PIXEL_CONVERT_PARAM stPixelConvertParam = new MyCamera.MV_PIXEL_CONVERT_PARAM();

                            stPixelConvertParam.pSrcData = stFrameOut.pBufAddr;//源数据
                            stPixelConvertParam.nWidth = stFrameOut.stFrameInfo.nWidth;//图像宽度
                            stPixelConvertParam.nHeight = stFrameOut.stFrameInfo.nHeight;//图像高度
                            stPixelConvertParam.enSrcPixelType = stFrameOut.stFrameInfo.enPixelType;//源数据的格式
                            stPixelConvertParam.nSrcDataLen = stFrameOut.stFrameInfo.nFrameLen;

                            stPixelConvertParam.nDstBufferSize = (uint)nImageBufSize;
                            stPixelConvertParam.pDstBuffer = pImageBuf;//转换后的数据
                            stPixelConvertParam.enDstPixelType = MyCamera.MvGvspPixelType.PixelType_Gvsp_Mono8;
                            nRet = m_MyCamera.MV_CC_ConvertPixelType_NET(ref stPixelConvertParam);//格式转换
                            if (MyCamera.MV_OK != nRet)
                            {
                                break;
                            }
                            pTemp = pImageBuf;
                        }
                        try
                        {
                            HOperatorSet.GenImage1Extern(out Hobj, "byte", stFrameOut.stFrameInfo.nWidth, stFrameOut.stFrameInfo.nHeight, pTemp, IntPtr.Zero);

                        }
                        catch (System.Exception ex)
                        {
                            MessageBoxEX.Show(ex.ToString(), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
                            break;
                        }
                        m_MyCamera.MV_CC_FreeImageBuffer_NET(ref stFrameOut);
                    }
                    else
                    {
                        m_MyCamera.MV_CC_FreeImageBuffer_NET(ref stFrameOut);
                        continue;
                    }
                    //   HalconDisplay(m_Window, Hobj, stFrameOut.stFrameInfo.nHeight, stFrameOut.stFrameInfo.nWidth);
                    eventProcessImage(Hobj);
                    m_MyCamera.MV_CC_FreeImageBuffer_NET(ref stFrameOut);
                    Hobj.Dispose();
                }
                else
                {
                    continue;
                }
            }



            //int nRet = MyCamera.MV_OK;
            //MyCamera device = obj as MyCamera;
            //MyCamera.MV_FRAME_OUT stFrameOut = new MyCamera.MV_FRAME_OUT();

            //IntPtr pImageBuf = IntPtr.Zero;
            //int nImageBufSize = 0;

            //HObject Hobj = new HObject();
            //IntPtr pTemp = IntPtr.Zero;

            //MyCamera.MV_FRAME_OUT stFrameInfo = new MyCamera.MV_FRAME_OUT();
            //MyCamera.MV_DISPLAY_FRAME_INFO stDisplayInfo = new MyCamera.MV_DISPLAY_FRAME_INFO();

            //while (m_bGrabbing)
            //{
            //    nRet = m_MyCamera.MV_CC_GetImageBuffer_NET(ref stFrameInfo, 1000);
            //    if (MyCamera.MV_OK == nRet)
            //    {
            //        if (IsColorPixelFormat(stFrameOut.stFrameInfo.enPixelType))
            //        {
            //            if (stFrameOut.stFrameInfo.enPixelType == MyCamera.MvGvspPixelType.PixelType_Gvsp_RGB8_Packed)
            //            {
            //                pTemp = stFrameOut.pBufAddr;
            //            }
            //            else
            //            {
            //                if (IntPtr.Zero == pImageBuf || nImageBufSize < (stFrameOut.stFrameInfo.nWidth * stFrameOut.stFrameInfo.nHeight * 3))
            //                {
            //                    if (pImageBuf != IntPtr.Zero)
            //                    {
            //                        Marshal.FreeHGlobal(pImageBuf);
            //                        pImageBuf = IntPtr.Zero;
            //                    }

            //                    pImageBuf = Marshal.AllocHGlobal((int)stFrameOut.stFrameInfo.nWidth * stFrameOut.stFrameInfo.nHeight * 3);
            //                    if (IntPtr.Zero == pImageBuf)
            //                    {
            //                        break;
            //                    }
            //                    nImageBufSize = stFrameOut.stFrameInfo.nWidth * stFrameOut.stFrameInfo.nHeight * 3;
            //                }

            //                MyCamera.MV_PIXEL_CONVERT_PARAM stPixelConvertParam = new MyCamera.MV_PIXEL_CONVERT_PARAM();

            //                stPixelConvertParam.pSrcData = stFrameOut.pBufAddr;//源数据
            //                stPixelConvertParam.nWidth = stFrameOut.stFrameInfo.nWidth;//图像宽度
            //                stPixelConvertParam.nHeight = stFrameOut.stFrameInfo.nHeight;//图像高度
            //                stPixelConvertParam.enSrcPixelType = stFrameOut.stFrameInfo.enPixelType;//源数据的格式
            //                stPixelConvertParam.nSrcDataLen = stFrameOut.stFrameInfo.nFrameLen;

            //                stPixelConvertParam.nDstBufferSize = (uint)nImageBufSize;
            //                stPixelConvertParam.pDstBuffer = pImageBuf;//转换后的数据
            //                stPixelConvertParam.enDstPixelType = MyCamera.MvGvspPixelType.PixelType_Gvsp_RGB8_Packed;
            //                nRet = device.MV_CC_ConvertPixelType_NET(ref stPixelConvertParam);//格式转换
            //                if (MyCamera.MV_OK != nRet)
            //                {
            //                    break;
            //                }
            //                pTemp = pImageBuf;
            //            }

            //            try
            //            {
            //                HOperatorSet.GenImageInterleaved(out Hobj, (HTuple)pTemp, (HTuple)"rgb", (HTuple)stFrameOut.stFrameInfo.nWidth, (HTuple)stFrameOut.stFrameInfo.nHeight, -1, "byte", 0, 0, 0, 0, -1, 0);
            //            }
            //            catch (System.Exception ex)
            //            {
            //                MessageBoxEX.Show(ex.ToString(), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
            //                break;
            //            }
            //        }
            //        else if (IsMonoPixelFormat(stFrameOut.stFrameInfo.enPixelType))
            //        {
            //            if (stFrameOut.stFrameInfo.enPixelType == MyCamera.MvGvspPixelType.PixelType_Gvsp_Mono8)
            //            {
            //                pTemp = stFrameOut.pBufAddr;
            //            }
            //            else
            //            {
            //                if (IntPtr.Zero == pImageBuf || nImageBufSize < (stFrameOut.stFrameInfo.nWidth * stFrameOut.stFrameInfo.nHeight))
            //                {
            //                    if (pImageBuf != IntPtr.Zero)
            //                    {
            //                        Marshal.FreeHGlobal(pImageBuf);
            //                        pImageBuf = IntPtr.Zero;
            //                    }

            //                    pImageBuf = Marshal.AllocHGlobal((int)stFrameOut.stFrameInfo.nWidth * stFrameOut.stFrameInfo.nHeight);
            //                    if (IntPtr.Zero == pImageBuf)
            //                    {
            //                        break;
            //                    }
            //                    nImageBufSize = stFrameOut.stFrameInfo.nWidth * stFrameOut.stFrameInfo.nHeight;
            //                }

            //                MyCamera.MV_PIXEL_CONVERT_PARAM stPixelConvertParam = new MyCamera.MV_PIXEL_CONVERT_PARAM();

            //                stPixelConvertParam.pSrcData = stFrameOut.pBufAddr;//源数据
            //                stPixelConvertParam.nWidth = stFrameOut.stFrameInfo.nWidth;//图像宽度
            //                stPixelConvertParam.nHeight = stFrameOut.stFrameInfo.nHeight;//图像高度
            //                stPixelConvertParam.enSrcPixelType = stFrameOut.stFrameInfo.enPixelType;//源数据的格式
            //                stPixelConvertParam.nSrcDataLen = stFrameOut.stFrameInfo.nFrameLen;

            //                stPixelConvertParam.nDstBufferSize = (uint)nImageBufSize;
            //                stPixelConvertParam.pDstBuffer = pImageBuf;//转换后的数据
            //                stPixelConvertParam.enDstPixelType = MyCamera.MvGvspPixelType.PixelType_Gvsp_Mono8;
            //                nRet = device.MV_CC_ConvertPixelType_NET(ref stPixelConvertParam);//格式转换
            //                if (MyCamera.MV_OK != nRet)
            //                {
            //                    break;
            //                }
            //                pTemp = pImageBuf;
            //            }
            //            try
            //            {
            //                HOperatorSet.GenImage1Extern(out Hobj, "byte", stFrameOut.stFrameInfo.nWidth, stFrameOut.stFrameInfo.nHeight, pTemp, IntPtr.Zero);
            //            }
            //            catch (System.Exception ex)
            //            {
            //                MessageBoxEX.Show(ex.ToString(), "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
            //                break;
            //            }
            //        }
            //        else
            //        {
            //            device.MV_CC_FreeImageBuffer_NET(ref stFrameOut);
            //            continue;
            //        }
            //        //   HalconDisplay(m_Window, Hobj, stFrameOut.stFrameInfo.nHeight, stFrameOut.stFrameInfo.nWidth);
            //        eventProcessImage(Hobj);
            //        device.MV_CC_FreeImageBuffer_NET(ref stFrameOut);
            //    }
            //    else
            //    {

            //        Thread.Sleep(5);
            //    }
            //}

            //if (pImageBuf != IntPtr.Zero)
            //{
            //    Marshal.FreeHGlobal(pImageBuf);
            //    pImageBuf = IntPtr.Zero;
            //}
        }
       
        private bool RemoveCustomPixelFormats(MyCamera.MvGvspPixelType enPixelFormat)
        {
            Int32 nResult = ((int)enPixelFormat) & (unchecked((Int32)0x80000000));
            if (0x80000000 == nResult)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        public void StartGrab()
        {
            // ch:标志位置位true | en:Set position bit true


            m_bGrabbing = true;

            m_hReceiveThread = new Thread(ReceiveImageWorkThread);
            m_hReceiveThread.Start(m_pMyCamera);

            // ch:开始采集 | en:Start Grabbing
            int nRet = m_MyCamera.MV_CC_StartGrabbing_NET();
            if (MyCamera.MV_OK != nRet)
            {
                m_bGrabbing = false;
                m_hReceiveThread.Join();
               
                return;
            }
        }
        //停止采集
        public void StopGrab()
        {

            // ch:标志位设为false | en:Set flag bit false
            m_bGrabbing = false;
            m_hReceiveThread.Join();

            // ch:停止采集 | en:Stop Grabbing
            int nRet = m_MyCamera.MV_CC_StopGrabbing_NET();
            if (nRet != MyCamera.MV_OK)
            {
                //ShowErrorMsg("Stop Grabbing Fail!", nRet);
            }
        }
        //软触发执行
        public void SoftTrigger()
        {
            // ch:触发命令 | en:Trigger command
            int nRet = m_MyCamera.MV_CC_SetCommandValue_NET("TriggerSoftware");
            if (MyCamera.MV_OK != nRet)
            {
              // ShowErrorMsg("Trigger Software Fail!", nRet);
            }
        }

        public void SetExposure(float Exposure)
        {

            m_MyCamera.MV_CC_SetEnumValue_NET("ExposureAuto", 0);
            int nRet = m_MyCamera.MV_CC_SetFloatValue_NET("ExposureTime", Exposure);
            if (nRet != MyCamera.MV_OK)
            {
                MessageBoxEX.Show(res.GetString("setTimeFail") + "\n" + nRet, "", MessageBoxButtons.OK, new string[] { res.GetString("sure") });
            }
        }

    }
}
