﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\PdfiumViewer.Native.x86_64.v8-xfa.2018.4.8.256\build\PdfiumViewer.Native.x86_64.v8-xfa.props" Condition="Exists('..\packages\PdfiumViewer.Native.x86_64.v8-xfa.2018.4.8.256\build\PdfiumViewer.Native.x86_64.v8-xfa.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8424675C-E35F-4EA5-99A2-E09F287479FA}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>BingSolhalcon</RootNamespace>
    <AssemblyName>BingSolhalcon</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>发布\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Prefer32Bit>false</Prefer32Bit>
    <DocumentationFile>bin\Debug\BingSolhalcon.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>App.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Basler.Pylon, Version=*******, Culture=neutral, PublicKeyToken=e389355f398382ab, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\Basler.Pylon.dll</HintPath>
    </Reference>
    <Reference Include="Caliper, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\Caliper.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.BonusSkins.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.BonusSkins.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Data.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.Data.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.DataAccess.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.DataAccess.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.DataAccess.v17.2.UI, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.DataAccess.v17.2.UI.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.DemoData.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.DemoData.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.DemoData.v17.2.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.DemoData.v17.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.ExpressApp.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.ExpressApp.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.ExpressApp.Xpo.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.ExpressApp.Xpo.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Images.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.Images.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Office.v17.2.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.Office.v17.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Persistent.Base.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.Persistent.Base.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Printing.v17.2.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.Printing.v17.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v17.2.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.RichEdit.v17.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Snap.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.Snap.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Snap.v17.2.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.Snap.v17.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Snap.v17.2.Extensions, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.Snap.v17.2.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Sparkline.v17.2.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.Sparkline.v17.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Tutorials.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.Tutorials.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.Utils.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v17.2.UI, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.Utils.v17.2.UI.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpo.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.Xpo.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraBars.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.XtraBars.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.XtraCharts.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v17.2.UI, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.XtraCharts.v17.2.UI.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v17.2.Wizard, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.XtraCharts.v17.2.Wizard.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.XtraEditors.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraGrid.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.XtraGrid.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraLayout.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.XtraLayout.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraNavBar.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.XtraNavBar.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraPrinting.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.XtraPrinting.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.XtraReports.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v17.2.Extensions, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.XtraReports.v17.2.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraRichEdit.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.XtraRichEdit.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraRichEdit.v17.2.Extensions, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.XtraRichEdit.v17.2.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraTreeList.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.XtraTreeList.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraVerticalGrid.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\DLL\DevExpress\DevExpress.XtraVerticalGrid.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="EntFxPlc, Version=3.4.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\EntFxPlc.dll</HintPath>
    </Reference>
    <Reference Include="GemBox.ExcelLite, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\GemBox.ExcelLite.dll</HintPath>
    </Reference>
    <Reference Include="halcondotnet, Version=12.0.0.0, Culture=neutral, PublicKeyToken=4973bed59ddbf2b8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\halcondotnet.dll</HintPath>
    </Reference>
    <Reference Include="hwindows, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\hwindows.dll</HintPath>
    </Reference>
    <Reference Include="Interop.ActProgTypeLib, Version=*******, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>True</EmbedInteropTypes>
      <HintPath>..\DLL\Interop.ActProgTypeLib.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.15.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.15\lib\net45\log4net.dll</HintPath>
    </Reference>
    <Reference Include="MvCameraControl.Net, Version=3.4.0.1, Culture=neutral, PublicKeyToken=52fddfb3f94be800, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\MvCameraControl.Net.dll</HintPath>
    </Reference>
    <Reference Include="MvStereoAppSDK.Net, Version=1.3.0.3, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\MvStereoAppSDK.Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI">
      <HintPath>..\DLL\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML">
      <HintPath>..\DLL\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net">
      <HintPath>..\DLL\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats">
      <HintPath>..\DLL\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="PdfiumViewer, Version=2.13.0.0, Culture=neutral, PublicKeyToken=91e4789cfb0609e0, processorArchitecture=MSIL">
      <HintPath>..\packages\PdfiumViewer.2.13.0.0\lib\net20\PdfiumViewer.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="SearchPattem, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\SearchPattem.dll</HintPath>
    </Reference>
    <Reference Include="SQlFun, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\SQlFun.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Management" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WHC.Framework.BaseUI, Version=*******, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\WHC.Framework.BaseUI.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="_3DFun">
      <HintPath>..\DLL\_3DFun.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="HKCameraLink.cs" />
    <Compile Include="ConfigIni.cs" />
    <Compile Include="HKCamera.cs" />
    <Compile Include="HKVision_3D.cs" />
    <Compile Include="MultiLanguage.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="resources\nonUIresx.en.Designer.cs">
      <DependentUpon>nonUIresx.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="resources\nonUIresx.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>nonUIresx.resx</DependentUpon>
    </Compile>
    <Compile Include="PcComPlc.cs" />
    <Compile Include="resources\nonUIresx.fr.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>nonUIresx.fr.resx</DependentUpon>
    </Compile>
    <Compile Include="SqlFun.cs" />
    <Compile Include="UI\PdfViewer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\PdfViewer.Designer.cs">
      <DependentUpon>PdfViewer.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\CameraParaSet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\CameraParaSet.Designer.cs">
      <DependentUpon>CameraParaSet.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\CircleCaliper.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\CircleCaliper.Designer.cs">
      <DependentUpon>CircleCaliper.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\CListBAdd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\CListBAdd.Designer.cs">
      <DependentUpon>CListBAdd.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\CListB_SelRightButtom.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\CListB_SelRightButtom.Designer.cs">
      <DependentUpon>CListB_SelRightButtom.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\About.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\About.Designer.cs">
      <DependentUpon>About.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\ColorSelect.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\ColorSelect.Designer.cs">
      <DependentUpon>ColorSelect.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataBase.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\DataBase.Designer.cs">
      <DependentUpon>DataBase.cs</DependentUpon>
    </Compile>
    <Compile Include="Datainteraction.cs" />
    <Compile Include="UI\Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\FormRegister.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\FormRegister.Designer.cs">
      <DependentUpon>FormRegister.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\HKLaser.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\HKLaser.Designer.cs">
      <DependentUpon>HKLaser.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\LogIn.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\LogIn.Designer.cs">
      <DependentUpon>LogIn.cs</DependentUpon>
    </Compile>
    <Compile Include="Networking\ClientMgr.cs" />
    <Compile Include="Networking\ServerMgr.cs" />
    <Compile Include="Networking\Socket.cs" />
    <Compile Include="UI\ModifyPassword.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\ModifyPassword.Designer.cs">
      <DependentUpon>ModifyPassword.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\NPOI.cs" />
    <Compile Include="UI\Patterm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Patterm.Designer.cs">
      <DependentUpon>Patterm.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Socket.cs" />
    <Compile Include="SoftReg.cs" />
    <Compile Include="UI\S7communication.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\S7communication.designer.cs">
      <DependentUpon>S7communication.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Setting.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Setting.Designer.cs">
      <DependentUpon>Setting.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\Technology.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\Technology.Designer.cs">
      <DependentUpon>Technology.cs</DependentUpon>
    </Compile>
    <Compile Include="WindowParaSet.cs" />
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="resources\nonUIresx.en.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>nonUIresx.en.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="resources\nonUIresx.es.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="resources\nonUIresx.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>nonUIresx.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="resources\nonUIresx.zh.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="resources\nonUIresx.fr.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>nonUIresx.fr.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CameraParaSet.en.resx">
      <DependentUpon>CameraParaSet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CameraParaSet.es.resx">
      <DependentUpon>CameraParaSet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CameraParaSet.fr.resx">
      <DependentUpon>CameraParaSet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CameraParaSet.resx">
      <DependentUpon>CameraParaSet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CameraParaSet.zh.resx">
      <DependentUpon>CameraParaSet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CircleCaliper.en.resx">
      <DependentUpon>CircleCaliper.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CircleCaliper.es.resx">
      <DependentUpon>CircleCaliper.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CircleCaliper.fr.resx">
      <DependentUpon>CircleCaliper.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CircleCaliper.resx">
      <DependentUpon>CircleCaliper.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CircleCaliper.zh.resx">
      <DependentUpon>CircleCaliper.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CListBAdd.en.resx">
      <DependentUpon>CListBAdd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CListBAdd.es.resx">
      <DependentUpon>CListBAdd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CListBAdd.resx">
      <DependentUpon>CListBAdd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CListBAdd.zh.resx">
      <DependentUpon>CListBAdd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CListB_SelRightButtom.en.resx">
      <DependentUpon>CListB_SelRightButtom.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CListB_SelRightButtom.es.resx">
      <DependentUpon>CListB_SelRightButtom.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CListB_SelRightButtom.resx">
      <DependentUpon>CListB_SelRightButtom.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\CListB_SelRightButtom.zh.resx">
      <DependentUpon>CListB_SelRightButtom.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\About.en.resx">
      <DependentUpon>About.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\About.es.resx">
      <DependentUpon>About.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\About.resx">
      <DependentUpon>About.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\About.zh.resx">
      <DependentUpon>About.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\ColorSelect.en.resx">
      <DependentUpon>ColorSelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\ColorSelect.es.resx">
      <DependentUpon>ColorSelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\ColorSelect.resx">
      <DependentUpon>ColorSelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\ColorSelect.zh.resx">
      <DependentUpon>ColorSelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataBase.en.resx">
      <DependentUpon>DataBase.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataBase.es.resx">
      <DependentUpon>DataBase.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataBase.fr.resx">
      <DependentUpon>DataBase.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataBase.resx">
      <DependentUpon>DataBase.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataBase.zh.resx">
      <DependentUpon>DataBase.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Form1.en.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Form1.es.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Form1.fr.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Form1.zh.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\FormRegister.en.resx">
      <DependentUpon>FormRegister.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\FormRegister.es.resx">
      <DependentUpon>FormRegister.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\FormRegister.fr.resx">
      <DependentUpon>FormRegister.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\FormRegister.resx">
      <DependentUpon>FormRegister.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\FormRegister.zh.resx">
      <DependentUpon>FormRegister.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\HKLaser.en.resx">
      <DependentUpon>HKLaser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\HKLaser.fr.resx">
      <DependentUpon>HKLaser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\HKLaser.resx">
      <DependentUpon>HKLaser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\LogIn.en.resx">
      <DependentUpon>LogIn.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\LogIn.es.resx">
      <DependentUpon>LogIn.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\LogIn.fr.resx">
      <DependentUpon>LogIn.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\LogIn.resx">
      <DependentUpon>LogIn.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\ModifyPassword.en.resx">
      <DependentUpon>ModifyPassword.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\ModifyPassword.es.resx">
      <DependentUpon>ModifyPassword.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\ModifyPassword.fr.resx">
      <DependentUpon>ModifyPassword.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\ModifyPassword.resx">
      <DependentUpon>ModifyPassword.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Patterm.en.resx">
      <DependentUpon>Patterm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Patterm.es.resx">
      <DependentUpon>Patterm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Patterm.fr.resx">
      <DependentUpon>Patterm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Patterm.resx">
      <DependentUpon>Patterm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Patterm.zh.resx">
      <DependentUpon>Patterm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\PdfViewer.resx">
      <DependentUpon>PdfViewer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\S7communication.en.resx">
      <DependentUpon>S7communication.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\S7communication.resx">
      <DependentUpon>S7communication.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\S7communication.zh.resx">
      <DependentUpon>S7communication.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Setting.en.resx">
      <DependentUpon>Setting.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Setting.es.resx">
      <DependentUpon>Setting.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Setting.fr.resx">
      <DependentUpon>Setting.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Setting.resx">
      <DependentUpon>Setting.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Technology.en.resx">
      <DependentUpon>Technology.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Technology.es.resx">
      <DependentUpon>Technology.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Technology.fr.resx">
      <DependentUpon>Technology.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Technology.resx">
      <DependentUpon>Technology.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\Technology.zh.resx">
      <DependentUpon>Technology.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="resources\resource.csv" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Classlib\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\MemoryDiagnostics\MemoryDiagnostics.csproj">
      <Project>{b4b9c3e1-7e8b-4a9c-95e3-6df7a1e9d1a4}</Project>
      <Name>MemoryDiagnostics</Name>
    </ProjectReference>
    <ProjectReference Include="..\S7Communication\CSharp7\Sharp7Library\Sharp7Library.csproj">
      <Project>{36844b98-047b-4185-a1b8-be424af171c2}</Project>
      <Name>Sharp7Library</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.4.5">
      <Visible>False</Visible>
      <ProductName>Windows Installer 4.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <None Include="resources\Excel.ICO" />
  </ItemGroup>
  <ItemGroup>
    <None Include="resources\Circle_Yellow.png" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App.ico" />
    <Content Include="model\registerpicture\00616C11C.bmp" />
    <Content Include="model\registerpicture\02217C26C.bmp" />
    <Content Include="model\registerpicture\02563C.bmp" />
    <Content Include="model\registerpicture\02563H.bmp" />
    <Content Include="model\registerpicture\05018C13C.bmp" />
    <Content Include="model\registerpicture\05517C08C.bmp" />
    <Content Include="model\registerpicture\07018C28C.bmp" />
    <Content Include="model\registerpicture\07019C15C.bmp" />
    <Content Include="model\registerpicture\07118C10.bmp" />
    <Content Include="model\registerpicture\07118C10C.bmp" />
    <Content Include="model\registerpicture\08319F10H.bmp" />
    <Content Include="model\registerpicture\08719F10C.bmp" />
    <Content Include="model\registerpicture\1C.bmp" />
    <Content Include="model\registerpicture\1H.bmp" />
    <None Include="resources\logo.jpg" />
    <Content Include="resources\2022-06-27_2.bmp" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
  </PropertyGroup>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\PdfiumViewer.Native.x86_64.v8-xfa.2018.4.8.256\build\PdfiumViewer.Native.x86_64.v8-xfa.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\PdfiumViewer.Native.x86_64.v8-xfa.2018.4.8.256\build\PdfiumViewer.Native.x86_64.v8-xfa.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>