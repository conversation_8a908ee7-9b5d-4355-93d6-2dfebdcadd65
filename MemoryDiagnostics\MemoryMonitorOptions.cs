using System;
using System.Collections.Generic;

namespace MemoryDiagnostics
{
    public sealed class MemoryMonitorOptions
    {
        public TimeSpan SampleInterval { get; set; } = TimeSpan.FromMinutes(1);
        public bool IncludeControls { get; set; } = false;
        public int MaxDepth { get; set; } = 1;
        public string LogDirectory { get; set; } = "logs";
        public int TopNForDeepDive { get; set; } = 0; // 0 表示不递归 Top-N

        // 自定义类型估算器注册表（第三方类型可在此挂接更精准的估算）
        public Dictionary<Type, Func<object, long>> CustomEstimators { get; } = new Dictionary<Type, Func<object, long>>();
    }
}