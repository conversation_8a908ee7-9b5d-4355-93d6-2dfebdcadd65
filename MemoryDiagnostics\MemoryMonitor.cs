using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Threading;

namespace MemoryDiagnostics
{
    public sealed class MemoryMonitor : IDisposable
    {
        private readonly object _root;
        private readonly MemoryMonitorOptions _options;
        private readonly MemorySizeEstimator _estimator = new MemorySizeEstimator();
        private readonly MemoryLogWriter _writer;
        private Timer _timer;
        private volatile bool _running;

        public MemoryMonitor(object root, MemoryMonitorOptions options)
        {
            _root = root ?? throw new ArgumentNullException(nameof(root));
            _options = options ?? new MemoryMonitorOptions();
            _writer = new MemoryLogWriter(_options.LogDirectory);
        }

        public void Start()
        {
            if (_running) return;
            _running = true;
            _timer = new Timer(TimerCallback, null, TimeSpan.Zero, _options.SampleInterval);
        }

        public void Stop()
        {
            _running = false;
            _timer?.Change(Timeout.Infinite, Timeout.Infinite);
            _timer?.Dispose();
            _timer = null;
        }

        public void TriggerOnce()
        {
            TimerCallback(null);
        }

        private void TimerCallback(object state)
        {
            if (!_running && state != null) return;
            try
            {
                DateTime now = DateTime.Now;
                _writer.AppendHeaderIfNeeded(now);

                var ctx = new MemoryEstimationContext
                {
                    MaxDepth = _options.MaxDepth,
                    IncludeControls = _options.IncludeControls,
                    CountObjectHeader = true
                };

                foreach (var entry in EnumerateTopLevelMembers(_root))
                {
                    long est = 0;
                    long count = 0;
                    string notes = string.Empty;

                    object value = entry.Value;
                    if (value == null) continue;

                    // 自定义估算器优先
                    var valType = value.GetType();
                    var custom = FindCustomEstimator(valType);
                    if (custom != null)
                    {
                        try { est = custom(value); notes = "CustomEstimator"; }
                        catch { est = 0; notes = "CustomEstimatorError"; }
                    }
                    else
                    {
                        est = _estimator.Estimate(value, ctx);
                    }

                    // 常见集合尝试提供 item 计数
                    count = TryGetItemCount(value);

                    // GC 指标
                    long gcTotal = GC.GetTotalMemory(false);
                    int g0 = GC.CollectionCount(0);
                    int g1 = GC.CollectionCount(1);
                    int g2 = GC.CollectionCount(2);

                    _writer.AppendLine(now, entry.Path, valType.FullName, est, count, notes, gcTotal, g0, g1, g2);
                }
            }
            catch
            {
                // swallow
            }
        }

        private Func<object, long> FindCustomEstimator(Type t)
        {
            // 精确匹配或基类/接口匹配
            foreach (var kv in _options.CustomEstimators)
            {
                if (kv.Key.IsAssignableFrom(t)) return kv.Value;
            }
            return null;
        }

        private static long TryGetItemCount(object value)
        {
            try
            {
                if (value is ICollection col) return col.Count;
                var t = value.GetType();
                var prop = t.GetProperty("Count", BindingFlags.Public | BindingFlags.Instance);
                if (prop != null && prop.PropertyType == typeof(int))
                {
                    var c = (int)prop.GetValue(value, null);
                    return c;
                }
            }
            catch { }
            return 0;
        }

        private struct MemberEntry
        {
            public string Path;
            public object Value;
        }

        private static IEnumerable<MemberEntry> EnumerateTopLevelMembers(object root)
        {
            var type = root.GetType();
            const BindingFlags flags = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.DeclaredOnly;

            foreach (var f in type.GetFields(flags))
            {
                if (f.IsStatic) continue;
                object val = null;
                try { val = f.GetValue(root); } catch { }
                yield return new MemberEntry { Path = type.Name + "." + f.Name, Value = val };
            }

            foreach (var p in type.GetProperties(flags))
            {
                if (!p.CanRead) continue;
                if (p.GetIndexParameters().Length > 0) continue; // 排除索引器
                object val = null;
                try { val = p.GetValue(root, null); } catch { }
                yield return new MemberEntry { Path = type.Name + "." + p.Name, Value = val };
            }
        }

        public void Dispose()
        {
            Stop();
        }
    }
}