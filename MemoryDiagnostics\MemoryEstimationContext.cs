using System.Collections.Generic;

namespace MemoryDiagnostics
{
    public sealed class MemoryEstimationContext
    {
        public int MaxDepth { get; set; } = 1;
        public bool IncludeControls { get; set; } = false;
        public ISet<object> Visited { get; } = new HashSet<object>(ReferenceEqualityComparer.Instance);
        public int CurrentDepth { get; set; } = 0;
        public bool CountObjectHeader { get; set; } = true;
    }
}